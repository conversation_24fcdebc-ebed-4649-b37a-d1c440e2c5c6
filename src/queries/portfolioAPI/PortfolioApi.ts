import { backOfficeEndpoints, endpoints } from "@/constants/api";
import { IErrorType } from "@/queries/accountAPI";
import {
  IActivePortfolio,
  IGetListOfPortfolios,
  IGetOnePortfolio,
  IPortfolioEditItem,
  IPostPortfolio
} from "@/queries/portfolioAPI/types";
import { apiService } from "@/services/apiService";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";

export const useGetListOfPortfoliosQuery = () =>
  useQuery<IGetListOfPortfolios>({
    queryKey: [backOfficeEndpoints.getListOfPortfolios]
  });

export const usePostPortfolioMutation = () =>
  useMutation<AxiosResponse, AxiosError<IErrorType>, IPostPortfolio, unknown>({
    mutationFn: (Body: IPostPortfolio) => apiService.post(backOfficeEndpoints.postPortfolio, Body)
  });

export const usePutPortfolioActiveMutation = () =>
  useMutation<AxiosResponse, AxiosError<IErrorType>, string>({
    mutationFn: (id: string) => apiService.put(backOfficeEndpoints.activePortfolio(id))
  });

export const usePutPortfolioDeActiveMutation = () =>
  useMutation<AxiosResponse, AxiosError<IErrorType>, string>({
    mutationFn: (id: string) => apiService.put(backOfficeEndpoints.deActivePortfolio(id))
  });

export const usePutPortfolioMutation = () =>
  useMutation<AxiosResponse, AxiosError<IErrorType>, IPortfolioEditItem, unknown>({
    mutationFn: (Body: IPortfolioEditItem) => apiService.put(backOfficeEndpoints.putPortfolio, Body)
  });

export const useGetOnePortfolioQuery = (id?: string) =>
  useQuery<IGetOnePortfolio>({
    queryKey: [backOfficeEndpoints.getBackOfficeOnePortfolio(id)],
    enabled: !!id
  });
