export interface IGetListOfPortfolios {
  data: IGetPortfoliosItem[];
  errorCode: null | string;
  errorMessage: null | string;
  isSuccess: null | string;
}

export interface IGetPortfoliosItem {
  dateOfEstablishment: string;
  dateOfEstablishmentPersian: string;
  fullName: string;
  fundPortfolioDataEntryType: number;
  fundType: number;
  id: string;
  isETF: boolean;
  isin: string | null;
  logoImageSVGBase64: string;
  symbolName: string;
}

export interface IPostPortfolio {
  symbolName: string;
  fullName: string;
  fundType: number;
  dateOfEstablishment: string;
  logoImageFile: string;
  isETF: boolean;
  // isin: string;
  displayOrder: number;
}

export interface IActivePortfolio {
  fundId: string;
}

export interface IPortfolioEditItem {
  fundId: string;
  symbolName: string;
  fullName: string;
  fundType: number;
  dateOfEstablishment: string;
  isETF: boolean;
  logoImageFile: string;
  // isin: string;
  displayOrder: number;
}

export interface IGetOnePortfolio {
  data: IGetOnePortfolioData;
  isSuccess: boolean;
  errorCode: null | string;
  errorMessage: null | string;
}

export interface IGetOnePortfolioData {
  file: string;
  fundId: string;
  symbolName: string;
  fullName: string;
  fundType: number;
  dateOfEstablishment: string;
  isETF: boolean;
  isin: string;
  logoImageSVGBase64: string;
}
