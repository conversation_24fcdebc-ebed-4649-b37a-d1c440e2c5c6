import { useDescription, useTsController } from "@ts-react/form";
import React from "react";
import ImageUploader from "./ImageUploader";

function ImageUploaderForm() {
  const {
    field: { onChange, value },
    error
  } = useTsController<string>();
  const { label } = useDescription();

  return <ImageUploader value={value} title={label} onChange={onChange} error={error} />;
}

export default ImageUploaderForm;
