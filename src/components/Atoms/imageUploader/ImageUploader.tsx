import { useSaveImagesMutation } from "@/queries/fileManagerApi";
import { twMerge } from "tailwind-merge";
import { bindBackOfficeImageUrl, convertFileToBlob } from "@/utils/file";
import Loading from "../loading";
import { TImageUploader } from "./types";

/**
 * This component just responsible for uploading image to FileManager APIs
 * If you want to control uploading process , do not use this
 */
function ImageUploader(props: TImageUploader) {
  const { value, onChange: onChangeProp, error, title, classNameProps } = props;

  const { mutateAsync: uploadImage, isLoading } = useSaveImagesMutation();
  const handleCallApi = (
    event: React.ChangeEvent<HTMLInputElement> | undefined,
    onChange: (fileName: string) => void
  ) => {
    const file = event?.target?.files?.[0];
    if (!file) {
      return;
    }

    convertFileToBlob(file).then(blob => {
      const formData = new FormData();
      formData.append("imageFile", blob, file.name);
      uploadImage(formData)
        .then(res => {
          onChange(res?.data?.data);
        })
        .catch(() => {});
    });
  };

  return (
    <div>
      <div className="text-sm">{title}</div>
      <div className="mt-auto flex items-center ">
        <input
          type="file"
          onChange={e => handleCallApi(e, onChangeProp)}
          className="file-input file-input-bordered w-full max-w-xs pr-0"
          dir="ltr"
          disabled={isLoading}
        />
        {isLoading ? (
          <Loading />
        ) : (
          <div className="relative">
            {value && (
              <div className={twMerge(value ? "h-12" : "h-12", classNameProps)}>
                <img
                  alt=""
                  src={value && bindBackOfficeImageUrl(value)}
                  className="mt-4 h-full rounded-lg object-cover"
                />
              </div>
            )}
          </div>
        )}
      </div>

      {error?.errorMessage && <div className={twMerge("text-xs text-pink")}>{error?.errorMessage}</div>}
    </div>
  );
}

export default ImageUploader;
