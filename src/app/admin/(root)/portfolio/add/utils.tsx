import { z } from "zod";
import {
  DatePickerSchema,
  ImageUploaderSchema,
  SelectSchema,
  SwitchSchema
} from "@/components/Organism/formBuilder/utils";

export const AddPortfolioFormSchema = z
  .object({
    symbolName: z.string({ required_error: "اجباری است" }).nonempty("اجباری است").describe(" نام "),
    fullName: z.string({ required_error: "اجباری است" }).nonempty("اجباری است").describe("نام کامل"),
    dateOfEstablishment: DatePickerSchema.describe("تاریخ تاسیس"),
    displayOrder: z.number({ required_error: "اجباری است" }).describe("ترتیب نمایش"),
    fundType: SelectSchema().describe("نوع صندوق"),
    isETF: SwitchSchema.describe("قابل معامله :"),
    logoImageFile: ImageUploaderSchema().describe("آیکون")
  })
  .required();

export const fundTypes = [
  { value: "0", label: "هیچکدام" },
  { value: "1", label: "صندوق درآمد ثابت" },
  { value: "2", label: "صندوق سهامی" },
  { value: "3", label: "صندوق مبتنی بر کالا" }
];
