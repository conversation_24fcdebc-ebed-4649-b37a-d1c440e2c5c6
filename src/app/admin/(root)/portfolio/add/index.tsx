"use client";

import { useRouter } from "next/navigation";
import Card from "@/components/Atoms/card";
import { FormBuilder } from "@/components/Organism/formBuilder";
import DangerPink from "@/assets/icons/danger-pink.svg";
import { useForm } from "react-hook-form";
import { usePostPortfolioMutation } from "@/queries/portfolioAPI";
import { IPortfolioFormFields } from "@/app/admin/(root)/portfolio/add/types";
import { AddPortfolioFormSchema, fundTypes } from "./utils";

function PortfolioAdd() {
  const router = useRouter();

  const { mutateAsync: postPortfolioAsync, isError, error } = usePostPortfolioMutation();

  const form = useForm<any>();

  const onSubmit = (data: IPortfolioFormFields) => {
    const newData = {
      ...data,
      fundType: Number(data?.fundType?.[0]?.value)
    };

    postPortfolioAsync(newData, {
      onSuccess: () => {
        router.back();
      }
    });
  };

  return (
    <Card className="h-full overflow-y-auto">
      {isError && (
        <div className="mb-8 mt-4 flex w-full gap-1 rounded-md bg-fuchsia-50 px-4 py-2 ">
          <DangerPink className="mt-0.5" />
          <span className="text-xs text-pink">{error?.response?.data?.errorMessage}</span>
        </div>
      )}

      <FormBuilder
        schema={AddPortfolioFormSchema}
        onSubmit={onSubmit}
        form={form}
        props={{
          fundType: {
            items: fundTypes,
            inputWrapperProps: { labelClass: "!top-0 !bg-white !px-2 z-50" },
            title: "تاریخ تاسیس"
          }
        }}
        formProps={{
          actionsPosition: "top",
          headerTitle: "افزودن پرتفو جدید",
          containerClass: "grid grid-cols-2 gap-4 mt-8",
          footerClassName: " flex-row-reverse ",
          buttonClass: "mt-4 ",
          cancelClass: "mt-4 ",

          buttonText: "ذخیره",
          cancelText: "انصراف",
          submitButtonVariant: "outLine",
          cancelButtonVariant: "fill",
          onCancel: () => router.back()
        }}
      />
    </Card>
  );
}

export default PortfolioAdd;
