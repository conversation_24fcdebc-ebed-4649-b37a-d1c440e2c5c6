/* eslint-disable jsx-a11y/label-has-associated-control */
import { ColDef, ColGroupDef, GridOptions } from "ag-grid-community";
import { dateConverter } from "@/utils/DateHelper";
import { ITableRowData } from "./types";

export function columnDefs(): (ColDef<ITableRowData> | ColGroupDef<ITableRowData>)[] | null | undefined {
  return [
    {
      headerName: "نام",
      unSortIcon: true,
      sortable: true,
      filter: "agTextColumnFilter",
      field: "symbolName",
      cellRenderer: ({ data }: { data: ITableRowData }) => <div>{data?.symbolName || "-"}</div>
    },
    {
      headerName: "نام کامل",
      unSortIcon: true,
      sortable: true,
      filter: "agTextColumnFilter",
      field: "fullName",
      cellRenderer: ({ data }: { data: ITableRowData }) => <div>{data?.fullName || "-"}</div>
    },
    {
      headerName: "تاریخ",
      unSortIcon: true,
      sortable: true,
      field: "dateOfEstablishment",
      cellRenderer: ({ data }: { data: ITableRowData }) => (
        <div className="ltr pr-3">
          {data?.dateOfEstablishment ? `${dateConverter(data?.dateOfEstablishment).format("YYYY/MM/DD")}` : "-"}
        </div>
      )
    },

    {
      headerName: "ترتیب نمایش",
      unSortIcon: true,
      sortable: true,
      field: "displayOrder",
      cellRenderer: ({ data }: { data: ITableRowData }) => (
        <div className="pr-3">{data?.displayOrder ? data?.displayOrder : "-"}</div>
      )
    },
    {
      headerName: "وضعیت",
      field: "isActive",
      cellRenderer: ({ data }: { data: ITableRowData }) => <div>{data?.isActive ? "فعال" : "غیرفعال"}</div>
    }
  ];
}

export const gridOptions: GridOptions = {
  enableRtl: true,
  onGridSizeChanged: () => {
    gridOptions.api?.sizeColumnsToFit();
  }
};
