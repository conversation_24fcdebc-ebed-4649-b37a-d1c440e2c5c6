"use client";

import Card from "@/components/Atoms/card";
import CardTitle from "@/components/Atoms/cardTitle";
import Table from "@/components/Organism/table";
import { useMemo, useRef } from "react";
import Button from "@/components/Atoms/adminButton";
import routes from "@/constants/routes";
import Link from "next/link";
import {
  useGetListOfPortfoliosQuery,
  usePutPortfolioActiveMutation,
  usePutPortfolioDeActiveMutation
} from "@/queries/portfolioAPI";
import { useActionModal } from "@/components/Molecules/confirmModal/hooks";
import { ITableRowData } from "@/app/admin/(root)/portfolio/(list)/types";
import { useAdminToast } from "@/hooks/useAdminToast";
import { useRouter } from "next/navigation";
import Loading from "@/components/Atoms/loading";
import { columnDefs, gridOptions } from "./utils";

function PortfolioList() {
  const gridRef = useRef<any>(null);

  const router = useRouter();

  const { openModal: openActionModal, closeModal: closeActionModal } = useActionModal();

  const { error: toastError } = useAdminToast();

  const { data, isLoading, refetch } = useGetListOfPortfoliosQuery();

  const { mutateAsync: putUserActiveAsync, isLoading: putUserActiveIsLoading } = usePutPortfolioActiveMutation();
  const { mutateAsync: putUserDeActiveAsync, isLoading: putUserDeActiveIsLoading } = usePutPortfolioDeActiveMutation();

  const toggleActive = (value: ITableRowData) => {
    const isActiveData = value?.isActive;

    if (!isActiveData) {
      putUserActiveAsync(value.id, {
        onSuccess: () => {
          refetch();
          closeActionModal();
        },
        onError: e => {
          toastError({ title: "خطا", subTitle: e.message });
          closeActionModal();
        }
      });
    } else {
      putUserDeActiveAsync(value.id, {
        onSuccess: () => {
          refetch();
          closeActionModal();
        },
        onError: e => {
          toastError({ title: "خطا", subTitle: e.message });
          closeActionModal();
        }
      });
    }
  };

  const columnDefsData = useMemo(() => columnDefs(), []);

  const handleOpenActiveModal = (value?: ITableRowData) => {
    if (!value) return;

    openActionModal({
      onConfirm: () => {
        toggleActive(value);
      },
      cancelBtnProps: { className: "w-1/2" },
      description: `آیا از ${value?.isActive ? " غیرفعال شدن " : " فعال شدن "}کاربر موردنظر اطمینان دارید؟ `,
      title: "تغییر وضعیت کاربر",
      okBtnProps: {
        disabled: putUserActiveIsLoading || putUserDeActiveIsLoading,
        className: "w-1/2"
      }
    });
  };

  const items = [
    {
      id: 1,
      title: "ویرایش",
      hasDisable: true,
      action: (value?: ITableRowData) => router.push(`${routes.editPortfolio(value?.id)}`)
    },
    {
      id: 2,
      title: "فعال/غیرفعال",
      hasDisable: true,
      action: (value?: ITableRowData) => handleOpenActiveModal(value)
    }
  ];

  return (
    <Card className="h-full">
      <div className="flex h-full flex-col justify-start">
        <CardTitle className="mb-4">
          <div className="flex w-full items-center justify-between">
            <div>لیست پرتفو ها</div>
            <div className="flex gap-1">
              <Link href={routes.addPortfolio}>
                <Button>افزودن</Button>
              </Link>
              <Button onClick={() => router.back()}>بازگشت</Button>
            </div>
          </div>
        </CardTitle>
        {isLoading ? (
          <div className="flex p-10 h-full items-center justify-center">
            <Loading />
          </div>
        ) : (
          <Table
            menuItems={items}
            processedKey="isEditable"
            processedValue={false}
            rowData={data?.data}
            columnDefs={columnDefsData}
            gridOptions={gridOptions}
            ref={gridRef}
            searchKey="symbol"
          />
        )}
      </div>
    </Card>
  );
}

export default PortfolioList;
