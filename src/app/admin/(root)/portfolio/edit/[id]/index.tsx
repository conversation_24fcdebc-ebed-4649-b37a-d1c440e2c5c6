"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Loading from "@/components/Atoms/loading";
import Card from "@/components/Atoms/card";
import { FormBuilder } from "@/components/Organism/formBuilder";
import DangerPink from "@/assets/icons/danger-pink.svg";
import { useGetOnePortfolioQuery, usePutPortfolioMutation } from "@/queries/portfolioAPI";
import { fundTypes } from "@/app/admin/(root)/portfolio/add/utils";
import { EditPortfolioFormSchema } from "@/app/admin/(root)/portfolio/edit/[id]/utils";
import { IPortfolioFormFields } from "@/app/admin/(root)/portfolio/edit/[id]/types";

function EditPortfolio(params: any) {
  const router = useRouter();
  const portfolioId = params?.params?.params?.id;

  const { data: userInfo, isFetching } = useGetOnePortfolioQuery(portfolioId);

  const { data: usersData } = userInfo || {};

  const { mutateAsync: putPortfolioAsync, isError, error } = usePutPortfolioMutation();

  const defaultFundTypeId = fundTypes?.find(i => Number(i?.value) === usersData?.fundType);

  const onSubmit = (data: IPortfolioFormFields) => {
    const updateData = { ...data, fundType: Number(data?.fundType?.[0]?.value), fundId: portfolioId };

    putPortfolioAsync(updateData, {
      onSuccess: () => {
        router.back();
      }
    });
  };

  return (
    <Card className="h-full">
      {isError && (
        <div className="mb-8 mt-4 flex w-full gap-1 rounded-md bg-fuchsia-50 px-4 py-2">
          <DangerPink className="mt-0.5" />
          <span className="text-xs text-pink">{error?.response?.data?.errorMessage}</span>
        </div>
      )}
      {isFetching ? (
        <div className="flex h-full items-center justify-center">
          <Loading />
        </div>
      ) : (
        <div>
          <FormBuilder
            schema={EditPortfolioFormSchema}
            onSubmit={onSubmit}
            props={{
              fundType: {
                items: fundTypes,
                inputWrapperProps: { labelClass: "!top-0 !bg-white !px-2 z-50" },
                title: "تاریخ تاسیس"
              }
            }}
            formProps={{
              actionsPosition: "top",
              headerTitle: "ویرایش پرتفو",
              containerClass: "grid grid-cols-2 gap-4 mt-8",
              buttonClass: "mt-4",
              buttonText: "ذخیره",
              cancelClass: "mt-4",
              cancelText: "انصراف",
              cancelButtonVariant: "outLine",
              onCancel: () => router.back()
            }}
            defaultValues={{ ...usersData, fundType: [defaultFundTypeId] }}
          />
        </div>
      )}
    </Card>
  );
}

export default EditPortfolio;
