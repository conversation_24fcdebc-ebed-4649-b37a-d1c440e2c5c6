import { z } from "zod";
import {
  DatePickerSchema,
  ImageUploaderSchema,
  SelectSchema,
  SwitchSchema
} from "@/components/Organism/formBuilder/utils";

export const EditPortfolioFormSchema = z
  .object({
    symbolName: z.string({ required_error: "اجباری است" }).nonempty("اجباری است").describe(" نام "),
    fullName: z.string({ required_error: "اجباری است" }).nonempty("اجباری است").describe("نام کامل"),
    dateOfEstablishment: DatePickerSchema.describe("تاریخ تاسیس"),
    fundType: SelectSchema().describe("نوع صندوق"),
    displayOrder: z.number({ required_error: "اجباری است" }).describe("ترتیب نمایش"),
    isETF: SwitchSchema.describe("قابل معامله :"),
    logoImageFile: ImageUploaderSchema().describe("آیکون")
  })
  .required();
