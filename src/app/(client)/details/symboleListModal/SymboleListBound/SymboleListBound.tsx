/* eslint-disable react/no-unescaped-entities */

"use client";

import Table from "@/components/Organism/table";
import { useMemo } from "react";
import { useGetFundAssetsBoundQuery } from "@/queries/symboleListAPI/symboleListAPI";
import { Spinner } from "@/components/Atoms/spinner";

import { CustomLoadingOverlay } from "@/components/Organism/table/utils";
import NoData from "@/components/Organism/table/NoData";
import { columnDefs, gridOptions } from "./utils";

interface ISymboleListBoundProps {
  fundId: string;
  gridRef: any;
}

export default function SymboleListBound({ fundId, gridRef }: ISymboleListBoundProps) {
  const {
    data: fundAssets,
    isError: isFundAssetsError,
    isLoading: isFundAssetsLoading
  } = useGetFundAssetsBoundQuery(fundId);

  // eslint-disable-next-line no-nested-ternary
  const data = isFundAssetsLoading ? null : fundAssets?.data ? fundAssets?.data : [];

  const columnDefsData = useMemo(() => columnDefs(), []);

  if (isFundAssetsLoading) {
    return (
      <div className="flex items-center justify-center p-10">
        <Spinner />
      </div>
    );
  }

  if (isFundAssetsError) {
    <div className="flex items-center justify-center p-10">
      <span className="text-13 text-white">خطایی رخ داده است!</span>
    </div>;
  }

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {isFundAssetsLoading || (!isFundAssetsLoading && data?.length) ? (
        <Table
          isClient
          ref={gridRef}
          rowData={data}
          columnDefs={columnDefsData}
          gridOptions={gridOptions}
          searchKey="symbol"
          loadingOverlayComponent={CustomLoadingOverlay}
          noRowsOverlayComponent={CustomLoadingOverlay}
        />
      ) : (
        !isFundAssetsLoading &&
        !data?.length && (
          <NoData
            isError={isFundAssetsError}
            customGridOPtions={gridOptions}
            columnDefs={columnDefsData}
            errorTitle="ارتباط با سرور برقرار نیست"
            noRowsTitle="موردی یافت نشد"
          />
        )
      )}
    </>
  );
}
