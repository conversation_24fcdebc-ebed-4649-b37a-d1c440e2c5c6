import { numbersUnitInToman, toFixed } from "@/utils/helpers";
import Danger from "@/assets/icons/danger.svg";
import { riskTranslate } from "@/app/(client)/details/[id]/components/histogram/utils";
import { twMerge } from "tailwind-merge";
import Loading from "@/components/Atoms/loading";
import { IFundValueTable, TPercentKeys } from "./types";
import { percentList, renderHover } from "./utils";

function FundValueTable({ title, data, className, isLoading, paramKey = "parametricValueAtRisk" }: IFundValueTable) {
  const showDanger = Object?.keys(percentList)?.some(key =>
    [1, 2, 3]?.includes(Number(data?.[key as TPercentKeys]?.[paramKey]?.riskLevel))
  );

  function findColor(key: TPercentKeys) {
    return (
      !data?.[key]?.[paramKey]?.valueAtRiskAmount ||
      (Number(data?.[key]?.[paramKey]?.valueAtRiskAmount) < 0 &&
        riskTranslate?.[data?.[key]?.[paramKey]?.riskLevel as keyof typeof riskTranslate] &&
        riskTranslate?.[data?.[key]?.[paramKey]?.riskLevel as keyof typeof riskTranslate]?.color)
    );
  }

  return (
    <div className={twMerge("rounded-lg border border-backgroundCardBackground overflow-hidden", className)}>
      <div className="bg-backgroundDarkRow rounded-t-lg flex justify-between pb-0.5">
        <div className="py-2 pr-[11px] text-mainText text-sm">{title}</div>
        {showDanger && <Danger className="text-warningBorder my-auto ml-2" />}
      </div>
      <table className="text-right rounded-lg border-t border-backgroundCardBackground w-full">
        <thead className="text-xs text-textSecondary font-bold">
          <tr>
            <th className="pr-2 pt-18px pb-[17px] min-w-[84px] border-l border-backgroundCardBackground bg-dark_black9">
              ضریب اطمینان
            </th>
            <th className="pr-2 pt-18px pb-[17px] min-w-[85px] border-l border-backgroundCardBackground bg-dark_black9">
              % VAR
            </th>
            <th className="pr-2 pt-18px pb-[17px] bg-dark_black9">VAR (تومان)</th>
          </tr>
        </thead>
        <tbody className="ltr text-sm">
          {Object?.entries(percentList)?.map(([key, value]) => (
            <tr>
              <td className="pr-2 pt-4 pb-[15px] font-bold min-w-[84px] border-l border-backgroundCardBackground bg-backgroundDarkRow">
                {value}
              </td>
              <td className="pr-2 pt-4 pb-[15px] font-normal min-w-[85px] border-l border-backgroundCardBackground bg-backgroundDarkRow">
                {isLoading ? (
                  <Loading size="xs" className="m-auto" />
                ) : (
                  `${toFixed(data?.[key as TPercentKeys]?.[paramKey]?.valueAtRiskPercent || 0)}٪`
                )}
              </td>
              <td
                className={twMerge(
                  "pr-2 pt-4 pb-[15px] bg-backgroundDarkRow",
                  findColor(key as TPercentKeys) as string
                )}
              >
                {data?.[key as TPercentKeys]?.[paramKey]?.valueAtRiskAmount &&
                  Number(data?.[key as TPercentKeys]?.[paramKey]?.valueAtRiskAmount) > 0 &&
                  renderHover()}
                {isLoading ? (
                  <Loading size="xs" className="m-auto" />
                ) : (
                  `${numbersUnitInToman(data?.[key as TPercentKeys]?.[paramKey]?.valueAtRiskAmount)}`
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default FundValueTable;
