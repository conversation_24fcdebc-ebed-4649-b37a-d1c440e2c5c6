import { Tooltip } from "@/components/Atoms/tooltip";
import { twMerge } from "tailwind-merge";

export function renderHover() {
  return (
    <Tooltip className="w-fit" content="مقدار سود در بدترین حالت" variant="green" placement="right">
      <div className="w-full h-0 rtl relative bottom-[15px] left-3">
        <div
          className={twMerge(
            "border-[6px] border-t-0 border-transparent border-b-[#A5F539] h-0 w-0 rotate-45 cursor-pointer"
          )}
        />
      </div>
    </Tooltip>
  );
}

export const percentList = {
  ninetyPercentValueAtRisk: "۹۰٪",
  ninetyFivePercentValueAtRisk: "۹۵٪",
  ninetyNinePercentValueAtRisk: "۹۹٪"
};
