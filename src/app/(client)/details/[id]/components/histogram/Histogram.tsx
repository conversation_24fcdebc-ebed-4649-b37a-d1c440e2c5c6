import ArrowRightIcon from "@/assets/icons/arrow-left.svg";
import Loading from "@/components/Atoms/loading";
import Switch from "@/components/Atoms/switch";
import BarChart from "@/components/Molecules/barChart/BarChart";
import SwitchTab from "@/components/Molecules/switchTab/SwitchTab";
import { useGetPortfolioReturnHistogramGraph } from "@/queries/FundChartsAPI";
import { useGetFundValueAtRiskQuery } from "@/queries/fundsApiFront";
import React, { memo, useState } from "react";
import FundValueTable from "./componenets/fundValueTable";
import { IHistogram } from "./types";
import { switchItems } from "./utils";

function Histogram({ onBackButtonClick, id }: IHistogram) {
  const [selectedTab, setSelectedTab] = useState(switchItems?.[0]);

  const [isChecked, setChecked] = useState(false);
  const onChangePercent = (e: React.ChangeEvent<HTMLInputElement>) => setChecked(e.target.checked);

  const { data, isLoading: tableIsLoading } = useGetFundValueAtRiskQuery(id);
  const { data: chartData, isLoading } = useGetPortfolioReturnHistogramGraph(id);

  return (
    <div className="grow flex flex-col">
      <div className="text-mainText font-normal pt-3.5 pb-[11px] pr-0.5 flex">
        <div className="flex items-center justify-between w-full ">
          <div className="w-1/4">
            <ArrowRightIcon
              className="rotate-180 w-5 inline ml-2 cursor-pointer"
              onClick={onBackButtonClick}
              aria-label="back button"
            />
            <span className="font-normal">ارزش در معرض خطر‌ (VAR)</span>
          </div>

          <div className="flex items-center gap-4">
            <span className=" text-xs text-white leading-4 ">نوع محاسبه: ماهانه </span>
            <span className=" text-xs text-white leading-4 ">بازه زمانی: سه ساله</span>
          </div>
        </div>
      </div>
      <div className="flex grow">
        <div className="2xl:w-1/4 w-[336px]">
          <FundValueTable
            paramKey="parametricValueAtRisk"
            isLoading={tableIsLoading}
            data={data?.data}
            className="mb-2"
            title="پارامتریک"
          />
          <FundValueTable
            paramKey="historicalValueAtRisk"
            isLoading={tableIsLoading}
            data={data?.data}
            title="تاریخی"
          />
        </div>
        <div className="grow mr-2 rounded-lg pb-14px">
          {isLoading ? (
            <div className="w-full h-full m-auto flex justify-center items-center align-middle flex-col">
              <Loading size="lg" />
              <div className="pt-3">درحال دریافت ...</div>
            </div>
          ) : (
            <div className="flex flex-col h-full rounded-lg bg-dark_black11">
              <div className="flex justify-between p-2 pt-1">
                <div className="relative p-0.5 pt-px flex justify-between items-center rounded w-fit z-50 bg-backgroundCardBackground top-1">
                  <SwitchTab
                    itemClassName="px-3 min-w-[90px]"
                    variant="filled"
                    items={switchItems}
                    activeTab={selectedTab?.id}
                    onSwitch={v => setSelectedTab(v)}
                  />
                </div>
                <div className="flex gap-1 items-center">
                  <span className="text-sm leading-4">نمایش به درصد</span>
                  <Switch
                    onChange={onChangePercent}
                    wrapperClassName="gap-0"
                    switchClassName="peer-checked:bg-semanticPrimary2 after:border-0 peer-checked:hover:bg-semanticPrimary2"
                    id="percent-switch"
                  />
                </div>
              </div>
              <BarChart
                data={chartData?.data?.graphPoints}
                isEnabledLineChart={selectedTab?.id === "linear-chart"}
                isShowInPercent={isChecked}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default memo(Histogram);
