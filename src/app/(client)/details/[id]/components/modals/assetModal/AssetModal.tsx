import PieChart from "@/components/Molecules/PieChart";
import { bindCompositionTypeToColor } from "@/components/Organism/singleFundCard/utils";
import Table from "@/components/Organism/table";
import { useGetFundAssetsCompositionQuery } from "@/queries/fundsApiFront";
import { useMemo } from "react";
import { twMerge } from "tailwind-merge";
import Styles from "./AssetModal.module.scss";
import { columnDefs, gridOptions } from "./utils";

function AssetModal({ fundId }: { fundId: string }) {
  const { data } = useGetFundAssetsCompositionQuery(fundId);

  const pieChartData = data?.data?.map(item => ({
    name: item.title,
    y: item.assetCompositionPercent,
    color: bindCompositionTypeToColor[item.assetCompositionType]
  }));

  const manipulatedData = data?.data?.map(item => ({
    ...item,
    name: item.title,
    color: bindCompositionTypeToColor[item.assetCompositionType]
  }));

  const columnDefsData = useMemo(() => columnDefs(), []);

  return (
    <div className="flex flex-col h-[544px] bg-dark_black rounded-lg">
      <div className="rounded-t-lg flex items-center justify-center pt-[6px] mb-[-3px]">
        {pieChartData && <PieChart id={fundId} isTooltipEnabled={false} data={pieChartData} width={164} height={164} />}
      </div>
      <div className={twMerge("flex-1 h-full rounded-b-lg bg-dark_black9", Styles.assetTable)}>
        <Table isClient rowData={manipulatedData} columnDefs={columnDefsData} gridOptions={gridOptions} />
      </div>
    </div>
  );
}

export default AssetModal;
