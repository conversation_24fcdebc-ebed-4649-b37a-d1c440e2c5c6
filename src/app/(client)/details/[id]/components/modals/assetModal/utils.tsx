import Hint from "@/assets/icons/hint.svg";
import { numbersUnitInToman, toDecimals } from "@/utils/helpers";
import { ColDef, ColGroupDef, GridOptions } from "ag-grid-community";
import { ITableRowData } from "./types";

export function columnDefs(): (ColDef<ITableRowData> | ColGroupDef<ITableRowData>)[] | null | undefined {
  return [
    {
      headerName: "نوع دارایی",
      field: "name",
      unSortIcon: true,
      sortable: true,
      width: 320,
      cellClass: "!px-[10px]  pt-[3px]",
      headerClass: "!pr-0 !-mr-[2px]  pt-2",
      cellRenderer: (data: any) => (
        <div className="w-full h-full relative">
          <Hint className="absolute -top-[2px] right-[1px] -mr-3 w-2 h-2" style={{ color: `${data?.data?.color}` }} />
          <div className="w-full h-full">{data?.data?.name}</div>
        </div>
      )
    },
    {
      headerName: "مبلغ(تومان)",
      field: "assetCompositionValue",
      unSortIcon: true,
      sortable: true,
      width: 240,
      cellClass: "!px-[9px]",
      headerClass: "!pr-0 pt-2",
      cellRenderer: ({ value }: { value: number }) => <div>{numbersUnitInToman(value)}</div>
    },
    {
      headerName: "درصد",
      field: "assetCompositionPercent",
      unSortIcon: true,
      sortable: true,
      width: 170,
      cellClass: "!px-[9px]",
      headerClass: "!pr-0 pt-2",
      cellRenderer: ({ value }: { value: number }) => <div>{toDecimals(value, 2, true)}%</div>
    },
    {
      headerName: "حد نصاب",
      width: 180,
      cellClass: "!px-[9px]",
      headerClass: "!pr-0 pt-2",
      cellRenderer: (data: any) => (
        <div className="flex items-center">
          <div>{data?.data?.maxAssetCompositionPercent}%</div>~<div>{data?.data?.minAssetCompositionPercent}%</div>
        </div>
      )
    }
  ];
}

export const gridOptions: GridOptions<ITableRowData> = {
  enableRtl: true,
  onGridSizeChanged: () => {
    gridOptions.api?.sizeColumnsToFit();
  }
};
