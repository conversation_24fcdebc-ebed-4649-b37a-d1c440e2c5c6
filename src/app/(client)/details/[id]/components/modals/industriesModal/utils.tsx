import Hint from "@/assets/icons/hint.svg";
import { toDecimals } from "@/utils/helpers";
import { ColDef, ColGroupDef, GridOptions } from "ag-grid-community";
import { ITableRowData } from "./types";

export function columnDefs(): (ColDef<ITableRowData> | ColGroupDef<ITableRowData>)[] | null | undefined {
  return [
    {
      headerName: "نام صنعت",
      field: "name",
      unSortIcon: true,
      width: 420,
      sortable: true,
      cellClass: "!px-[10px]  pt-[3px]",
      headerClass: "!pr-0 !-mr-[3px] pt-2 ",
      cellRenderer: (data: any) => (
        <div className="w-full h-full relative">
          <Hint className="absolute -top-[3px] right-0 -mr-3 w-2 h-2 " style={{ color: `${data?.data?.color}` }} />
          <div className="w-full h-full">{data?.data?.name}</div>
        </div>
      )
    },
    {
      headerName: "درصد",
      field: "sharePercent",
      unSortIcon: true,
      width: 100,
      sortable: true,
      cellClass: "!px-[9px]",
      headerClass: "!pr-[0px] pt-2",
      cellRenderer: ({ value }: { value: number }) => <div>{toDecimals(value, 2, true)}%</div>
    }
  ];
}

export const gridOptions: GridOptions<ITableRowData> = {
  enableRtl: true,
  onGridSizeChanged: () => {
    gridOptions.api?.sizeColumnsToFit();
  }
};
