import React, { useMemo } from "react";
import { twMerge } from "tailwind-merge";
import { useGetFundIndustriesSharePercentQuery } from "@/queries/fundsApiFront";
import Table from "@/components/Organism/table";
import PieChart from "@/components/Molecules/PieChart";
import { columnDefs, gridOptions } from "./utils";
import Styles from "../assetModal/AssetModal.module.scss";

function IndustriesModal({ fundId }: { fundId: string }) {
  const { data } = useGetFundIndustriesSharePercentQuery(fundId);

  const newData = data?.data.map(item => ({
    name: item.name,
    y: item.sharePercent
  }));

  const colors = ["#4895EF", "#4361EE", "#3F37C9", "#7209B7", "#B5179E", "#4CC9F0"];

  const manipulatedData = data?.data?.map((item, index) => ({
    ...item,
    color: colors[index]
  }));

  const columnDefsData = useMemo(() => columnDefs(), []);

  return (
    <div className="flex flex-col h-[496px] bg-dark_black rounded-lg pt-[6px]">
      <div className="rounded-t-lg flex items-center justify-center  mb-[-3px]">
        {newData && <PieChart id={fundId} isTooltipEnabled={false} data={newData} width={1646} height={164} />}
      </div>
      <div className={twMerge("flex-1 h-full rounded-b-lg bg-dark_black9", Styles.assetTable)}>
        <Table isClient rowData={manipulatedData} columnDefs={columnDefsData} gridOptions={gridOptions} />
      </div>
    </div>
  );
}

export default IndustriesModal;
