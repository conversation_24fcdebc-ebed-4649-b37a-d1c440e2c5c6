import { dateConverter } from "@/utils/DateHelper";
import { toFixed } from "@/utils/helpers";
import { Options } from "highcharts";
import { IRenderRecordParams } from "./type";

export const defaultColors = ["#26D5C0", "#BCD526"];

export const convertDate = (date: string) => {
  const d = new Date(date);
  const utc = Date.UTC(d.getFullYear(), d.getMonth(), d.getDate());
  return utc;
};

export const renderRecord = (params: IRenderRecordParams) => {
  const { dateTime, value, index, count } = params;

  if (index === count - 1) {
    return {
      x: convertDate(dateTime),
      y: value,
      marker: { enabled: true }
    };
  }

  return { x: convertDate(dateTime), y: value };
};
export function findInterval(showMonthlyPeriod?: boolean) {
  return showMonthlyPeriod ? 24 * 3600 * 1000 * 30 : null;
}
const getLineChartOptions = (props: {
  data: any[];
  chartColors?: string[];
  hasPercent?: boolean;
  hasPercentSign?: boolean;
  showMonthlyPeriod?: boolean;
}) => {
  const { data, chartColors, hasPercent, hasPercentSign } = props;
  const colors = chartColors || defaultColors;

  const options = {
    chart: {
      backgroundColor: "transparent",
      marginTop: 5,
      marginBottom: 61,
      animation: false,
      zooming: {
        type: "x",
        resetButton: {
          position: { x: 0, y: 3 },
          theme: {
            fill: "#F4F4F4",
            stroke: "transparent",
            class: "zoom-button",
            border: 0,
            r: 4,
            zIndex: 10,
            style: { color: "#161616", fontSize: "14px", fontWeight: "400" }
          }
        }
      }
    },
    title: { text: "" },
    subtitle: { text: "" },
    // xAxis: {
    //   type: "datetime",
    //   tickLength: 0,
    //   labels: {
    //     /* @ts-ignore */
    //     formatter() {
    //       /* @ts-ignore */
    //       const d = new Date(this.value);
    //       const format = "MM/DD";
    //       return dateConverter(d.toString()).format(format);
    //     },
    //     y: 34,
    //     style: { color: "#F4F4F4", fontSize: "12px", fontWeight: "400" }
    //   }
    // },
    yAxis: {
      title: "",
      gridLineColor: "#545454",
      gridLineWidth: 1,
      labels: {
        useHTML: true,
        /* @ts-ignore */
        // eslint-disable-next-line
        formatter: function () {
          if (hasPercent) {
            /* @ts-ignore */
            return `${toFixed(this.value * 100)}%`;
          }
          /* @ts-ignore */
          return `${toFixed(this.value)}${hasPercentSign ? "%" : ""}`;
        },
        style: { color: "#F4F4F4", zIndex: -1, fontFamily: "var(--font-yekan)" }
      }
    },
    tooltip: {
      shape: "rect",
      /* @ts-ignore */
      useHTML: true,
      shadow: false,
      backgroundColor: "#28282c",
      borderColor: "#545454",
      borderRadius: 8,
      borderWidth: 1,
      padding: 12,
      animation: false,
      // eslint-disable-next-line
      formatter: function () {
        let date1 = "";

        /* @ts-ignore */

        /* @ts-ignore */
        this.points.forEach(i => {
          /* @ts-ignore */
          const d = dateConverter(new Date(i.x).toString());
          date1 = d.format("YYYY/M/D");
        });

        let s = `
                <div class="tooltip">
                    <span class="date">${date1}</span>
                `;

        /* @ts-ignore */
        this.points.forEach((i, index) => {
          const { y: value, series, color } = i;
          /* @ts-ignore */
          const v = hasPercent ? value * 100 : value;

          s += `
                    <div>
                        <b>
                        ${`${toFixed(v as number, 3)}${hasPercent || hasPercentSign ? "%" : ""}`}
                        </b>
                        <span>
                            <pre>${series.name}</pre>
                            <i style="background:${color}"></i>
                        </span>

                    </div>
                `;
        });

        s += "</div>";

        return s;
      },
      shared: true
    },
    colors,
    series: data,
    plotOptions: {
      series: {
        lineWidth: 2,
        marker: { symbol: "circle", radius: 3, enabled: false },
        states: { inactive: { opacity: 1 } }
      }
    },
    credits: { enabled: false },
    legend: { enabled: false }
  } as Options;

  return options;
};

export default getLineChartOptions;
