"use client";

/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-array-index-key */

import { useEffect } from "react";
import { twMerge } from "tailwind-merge";
import { Tooltip } from "react-tooltip";
import { useGetFundColumnChartRiskManagementQuery } from "@/queries/fundsAPI";
import { ORGANIZATION_STATUS_LABELS as ORG } from "@/components/Molecules/heatMap/util";
import { getColorByValue } from "@/app/(client)/details/[id]/components/cards/util";

function TooltipHeatMapRiskManagement({
  fundId,
  tooltipId,
  refresh
}: {
  fundId: string;
  tooltipId: string;
  refresh: number;
}) {
  const { data: tooltipQuery, refetch } = useGetFundColumnChartRiskManagementQuery(fundId);
  const { data: tooltipData } = tooltipQuery || {};

  useEffect(() => {
    if (refresh) {
      refetch();
    }
  }, [refresh]);

  const l = "ریسک های ";

  const titles = [
    { title: l + ORG.CRITICAL, color: "#E35050" },
    { title: l + ORG.HIGH, color: "#E1AB11" },
    { title: l + ORG.MODERATE, color: "#FAEB8E" },
    { title: l + ORG.LOW, color: "#108554" }
  ];

  const {
    previousCriticalRiskCount = 0,
    previousCriticalRiskPercent = 0,
    criticalRiskCount = 0,
    criticalRiskPercent = 0,

    previousHighRiskCount = 0,
    previousHighRiskPercent = 0,
    highRiskCount = 0,
    highRiskPercent = 0,

    previousMediumRiskCount = 0,
    previousMediumRiskPercent = 0,
    mediumRiskCount = 0,
    mediumRiskPercent = 0,

    previousLowRiskCount = 0,
    previousLowRiskPercent = 0,
    lowRiskCount = 0,
    lowRiskPercent = 0
  } = tooltipData || {};

  const previousValues = [
    { count: previousCriticalRiskCount, percent: previousCriticalRiskPercent },
    { count: previousHighRiskCount, percent: previousHighRiskPercent },
    { count: previousMediumRiskCount, percent: previousMediumRiskPercent },
    { count: previousLowRiskCount, percent: previousLowRiskPercent }
  ];

  const currentValues = [
    { count: criticalRiskCount, percent: criticalRiskPercent },
    { count: highRiskCount, percent: highRiskPercent },
    { count: mediumRiskCount, percent: mediumRiskPercent },
    { count: lowRiskCount, percent: lowRiskPercent }
  ];

  const previousSum = previousValues.reduce((n, obj) => n + obj.count, 0);
  const currentSum = currentValues.reduce((n, obj) => n + obj.count, 0);

  return (
    <Tooltip
      id={tooltipId}
      float
      place="right-end"
      className="!p-3 !border !border-dark_black8 !bg-cardBackground !rounded-lg min-w-[184px] z-10 !mx-2 !opacity-100 [&>.react-tooltip-arrow]:border-[5px] [&>.react-tooltip-arrow]:border-cardBackground [&>.react-tooltip-arrow]:-mt-2"
      classNameArrow="mt-2"
    >
      <div className="flex flex-col leading-4 z-20 text-10 text-center text-borderLightGray">
        <div className="flex justify-end border-b border-b-borderBorderAndDivider">
          <span className="grow" />
          <span className="w-[61px] pb-[2px] text-center border-l border-l-borderBorderAndDivider">ریسک</span>
          <span className="w-[64px] pb-[2px] text-center">اقدام</span>
        </div>

        <div className="flex">
          <div className="flex flex-col gap-3 pt-2 pl-2">
            {titles.map((i, k) => (
              <div key={k} className="flex gap-2 text-10 items-center">
                <span className="w-3 h-3 ml-1 rounded-sm" style={{ background: i.color }} />
                <span className="w-[65px] text-right leading-4 h-4 whitespace-nowrap">{i.title}</span>
              </div>
            ))}
          </div>

          <div className="flex flex-col gap-3 w-[61px] px-2 pt-2 border-l border-l-borderBorderAndDivider">
            {previousValues.map((i, k) => (
              <div key={k} className="flex text-10 items-center justify-between">
                <span className="w-3 leading-4 h-4">{i.count}</span>
                <span className="leading-4 h-4 ltr">{Math.round(i.percent)} %</span>
              </div>
            ))}
            <span className="border-t border-t-borderBorderAndDivider">{previousSum}</span>
          </div>

          <div className="flex flex-col gap-3 px-2 pt-2 w-[64px]">
            {currentValues.map((i, k) => (
              <div key={k} className="flex text-10 justify-between items-center">
                <span className={twMerge("w-3 leading-4", getColorByValue(i.count))}>{i.count}</span>
                <span className={twMerge("leading-4 ltr", getColorByValue(i.percent))}>{Math.round(i.percent)} %</span>
              </div>
            ))}
            <span className="border-t border-t-borderBorderAndDivider">{currentSum}</span>
          </div>
        </div>
      </div>
    </Tooltip>
  );
}

export default TooltipHeatMapRiskManagement;
