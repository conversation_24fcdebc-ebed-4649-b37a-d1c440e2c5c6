import { FUND_TYPES } from "@/queries/fundsAPI/types";
import AlphaChart from "../marketRisk/AlphaChart";
import BetaChart from "../marketRisk/BetaChart";
import DeviationChart from "../marketRisk/DeviationChart";
import EfficiencyChart from "../marketRisk/EfficiencyChart";
import RiskValueChart from "../marketRisk/RiskValue";
import SharpChart from "../marketRisk/SharpChart";
import TrainerChart from "../marketRisk/TrainerChart";

const RenderChart = ({
  type,
  fundId,
  isShowRetrospect
}: {
  type: string;
  fundId: string;
  isShowRetrospect: boolean;
}) => {
  switch (type) {
    case FUND_TYPES.BETA:
      return <BetaChart id={fundId} isShowRetrospect={isShowRetrospect} />;
    case FUND_TYPES.DEVIATION:
      return <DeviationChart id={fundId} isShowRetrospect={isShowRetrospect} />;
    case FUND_TYPES.EFFICIENCY:
      return <EfficiencyChart id={fundId} isShowRetrospect={isShowRetrospect} />;
    case FUND_TYPES.TRAINER:
      return <TrainerChart id={fundId} isShowRetrospect={isShowRetrospect} />;
    case FUND_TYPES.SHARP:
      return <SharpChart id={fundId} isShowRetrospect={isShowRetrospect} />;
    case FUND_TYPES.ALPHA:
      return <AlphaChart id={fundId} isShowRetrospect={isShowRetrospect} />;
    case FUND_TYPES.ALPHA_ADJUSTED:
      return <AlphaChart id={fundId} isShowRetrospect={isShowRetrospect} />;
    case FUND_TYPES.RISK_VALUE:
      return <RiskValueChart id={fundId} isShowRetrospect={isShowRetrospect} />;
    default:
      return null;
  }
};

export default RenderChart;
