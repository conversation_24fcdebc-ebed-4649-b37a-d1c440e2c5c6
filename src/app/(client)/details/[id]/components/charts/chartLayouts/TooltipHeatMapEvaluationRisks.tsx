"use client";

import { useMemo } from "react";
import { useGetFundColumnChartEvaluationQuery } from "@/queries/fundsAPI";
import { Tooltip } from "react-tooltip";
import { ORGANIZATION_STATUS_LABELS as ORG } from "@/components/Molecules/heatMap/util";

function TooltipHeatMapEvaluationRisks({ fundId, tooltipId }: { fundId: string; tooltipId: string }) {
  const { data: tooltipQuery } = useGetFundColumnChartEvaluationQuery(fundId);
  const { data: tooltipData } = tooltipQuery || {};

  const tooltipRows = useMemo(() => {
    const {
      criticalRiskCount,
      criticalRiskPercent,
      highRiskCount,
      highRiskPercent,
      lowRiskCount,
      lowRiskPercent,
      mediumRiskCount,
      mediumRiskPercent
    } = tooltipData || {};

    const l = "ریسک های ";

    return [
      { title: l + ORG.CRITICAL, count: criticalRiskCount, percent: criticalRiskPercent, color: "#E35050" },
      { title: l + ORG.HIGH, count: highRiskCount, percent: highRiskPercent, color: "#E1AB11" },
      { title: l + ORG.MODERATE, count: mediumRiskCount, percent: mediumRiskPercent, color: "#FAEB8E" },
      { title: l + ORG.LOW, count: lowRiskCount, percent: lowRiskPercent, color: "#108554" }
    ];
  }, [tooltipData]);

  return (
    <Tooltip
      id={tooltipId}
      place="right-end"
      float
      className="!p-3 !border !border-dark_black8 !bg-cardBackground !rounded-lg min-w-[184px] z-10 !mx-2 !opacity-100 [&>.react-tooltip-arrow]:border-[5px] [&>.react-tooltip-arrow]:border-cardBackground [&>.react-tooltip-arrow]:-mt-2"
      classNameArrow="mt-2"
    >
      <div className="flex flex-col gap-2 leading-4 z-20 text-10 text-center text-borderLightGray">
        <div className="flex justify-end">
          <span className="w-8 text-left">تعداد</span>
          <span className="w-8 text-left">درصد</span>
        </div>
        {tooltipRows.map(i => (
          <div className="flex items-center">
            <span className="w-3 h-3 ml-1 rounded-sm" style={{ background: i.color }} />
            <span className="w-[80px] text-right">{i.title}</span>
            <span className="min-w-8 text-center">{i.count}</span>
            <span className="min-w-8 text-left ltr">{Math.round(i.percent || 0)} %</span>
          </div>
        ))}
      </div>
    </Tooltip>
  );
}

export default TooltipHeatMapEvaluationRisks;
