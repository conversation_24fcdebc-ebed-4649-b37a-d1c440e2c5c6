import { FUND_TABS } from "@/app/(client)/details/[id]/components/cards/type";
import { tabs } from "@/app/(client)/details/[id]/components/cards/util";
import CloseIcon from "@/assets/icons/Cancel.svg";
import MinimizeIcon from "@/assets/icons/minimize.svg";
import { FUND_LABELS, FUND_TYPES } from "@/queries/fundsAPI/types";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import { parseAsBoolean, parseAsInteger, parseAsString, useQueryStates } from "nuqs";
import FundChartFilter from "../../cards/FundChartFilter";
import Render<PERSON>hart from "./RenderChart";
import RenderInterestChart from "./RenderInterestChart";

function ChartModal(props: { fundId: string; hasCheckbox?: boolean; onCloseModal: () => void }) {
  const { fundId, hasCheckbox = true, onCloseModal } = props;

  const [queryStates, setQueryStates] = useQueryStates({
    activeTab: parseAsInteger.withDefault(tabs[0].id),
    type: parseAsString.withDefault(FUND_TYPES.EFFICIENCY),
    isShowRetrospect: parseAsBoolean.withDefault(true)
  });

  const { type, isShowRetrospect, activeTab } = queryStates;
  const title = `نمودار تغییرات ${FUND_LABELS[type]}` || "---";

  const onRetrospectCheck = () => setQueryStates({ isShowRetrospect: !isShowRetrospect });
  const { closeUserModal } = useUserModalStore();

  return (
    <>
      <div className="flex justify-between">
        <div className="flex gap-4 pb-3">
          {title}
          {hasCheckbox && (
            <FundChartFilter
              isCheckedRetrospect={isShowRetrospect}
              className="p-0 m-0 border-t-0"
              onChange={onRetrospectCheck}
            />
          )}
        </div>
        <CloseIcon
          onClick={onCloseModal}
          width={24}
          height={24}
          className="cursor-pointer text-borderLightGray"
          aria-label="close modal"
        />
      </div>

      {/* Render line chart based on active tab */}
      <div className="relative flex grow">
        {activeTab === FUND_TABS.MARKET_RISK && (
          <RenderChart type={type} isShowRetrospect={isShowRetrospect} fundId={fundId} />
        )}
        {activeTab === FUND_TABS.INTEREST_RISK && <RenderInterestChart type={type} fundId={fundId} />}
        <MinimizeIcon
          onClick={closeUserModal}
          className="absolute w-6 h-6 left-3 bottom-3 cursor-pointer"
          aria-label="minimize chart"
        />
      </div>
    </>
  );
}

export default ChartModal;
