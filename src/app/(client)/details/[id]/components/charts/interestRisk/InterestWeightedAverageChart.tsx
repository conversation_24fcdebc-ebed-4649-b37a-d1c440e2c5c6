"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import {
  getSeriesOfInterestRiskFormat,
  getSummaryInterestOptions
} from "@/app/(client)/details/[id]/components//cards/util";
import HighChart from "@/components/Molecules/highChart";
import { useGetMarketValueWeightedYtmAverageHistoryChart } from "@/queries/FundChartsAPI";
import { useMemo } from "react";
import Loading from "@/components/Atoms/loading";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function InterestWeightedAverageChart() {
  const { data: raw, isLoading } = useGetMarketValueWeightedYtmAverageHistoryChart();
  const { data } = raw || {};

  const { graphPoints = [], yearAverage = 0 } = data || {};
  const summary = getSummaryInterestOptions(yearAverage, 0);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        field1: {
          id: "riskFreeRate",
          title: "نرخ بهره بدون ریسک"
        },
        hasPercent: true
      }),
    [data]
  );

  return (
    <ChartWrapper summary={<SummaryBox hasPercent data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default InterestWeightedAverageChart;
