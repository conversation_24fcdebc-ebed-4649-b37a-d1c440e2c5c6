"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import { useGetFundDurationHistoryChart } from "@/queries/FundChartsAPI";
import HighChart from "@/components/Molecules/highChart";
import {
  getSeriesOfInterestRiskFormat,
  getSummaryInterestOptions2
} from "@/app/(client)/details/[id]/components//cards/util";
import { parseAsBoolean, useQueryStates } from "nuqs";
import Loading from "@/components/Atoms/loading";
import { IDetailChartProps } from "../type";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function ModifiedDurationChart({ id }: IDetailChartProps) {
  const { data: raw, isLoading } = useGetFundDurationHistoryChart({ fundId: id });
  const { data } = raw || {};

  const [queryStates] = useQueryStates({
    isShowMarketValue: parseAsBoolean.withDefault(true)
  });
  const { isShowMarketValue } = queryStates;

  const { graphPoints = [], modifiedDurationYearAverage = 0, marketModifiedDurationYearAverage = 0 } = data || {};

  const colors = ["#8871BA", "#F4F4F4"];

  const summaryValues = [modifiedDurationYearAverage, marketModifiedDurationYearAverage];

  const summary = getSummaryInterestOptions2(summaryValues, colors);

  const finalGraphPoints = graphPoints?.map(item => ({
    checkpointDate: item?.checkpointDate,
    modifiedDuration: item?.modifiedDuration,
    marketModifiedDuration: item?.marketModifiedDuration
  }));

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints: finalGraphPoints,
        checked: !!isShowMarketValue,
        field1: {
          id: "modifiedDuration",
          title: "پرتفو"
        },
        field2: {
          id: "marketModifiedDuration",
          title: "بازار"
        },
        hasPercent: false,
        colors
      }),
    [data, isShowMarketValue]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} hasPercent={false} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default ModifiedDurationChart;
