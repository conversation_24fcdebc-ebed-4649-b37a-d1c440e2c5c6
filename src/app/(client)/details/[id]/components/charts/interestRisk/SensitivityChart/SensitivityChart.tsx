/* eslint-disable react/no-this-in-sfc */
import { useState } from "react";
import { twMerge } from "tailwind-merge";
import { setMinChartRange } from "@/utils/helpers";
import { useGetInterestSensitivitiesByChangeChart, useGetInterestSensitivitiesChart } from "@/queries/FundChartsAPI";
import { ColumnChart } from "@/components/Molecules/columnChart";
import CloseIcon from "@/assets/icons/close-user-modal.svg";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import SensitivitySearch from "./SensitivitySearch";
import { getZeroColumnChartOption, getZeroSingleColumnChartOption } from "./util";

function SensitivityChart(props: { fundId: string }) {
  const { fundId } = props;

  const [interestChange, setInterestChange] = useState(0);
  const { closeUserModal } = useUserModalStore();

  const { data: sensitiveQuery } = useGetInterestSensitivitiesChart(fundId);
  const { data: sensitiveData } = sensitiveQuery || {};

  const { data: sensitiveChangeQuery } = useGetInterestSensitivitiesByChangeChart(fundId, interestChange);
  const { data: sensitiveChangeData } = sensitiveChangeQuery || {};

  const onChangeSensitivity = (v: number) => setInterestChange(v);
  const mainChartData = sensitiveData?.map(i => i.portfolioValueChange / 10) || [];

  const min = Math.min.apply(null, mainChartData);
  const max = Math.max.apply(null, mainChartData);

  const categories = ["-3", "-2", "-1", "-0.5", "0", "0.5", "1", "2", "3"];

  const seriesMainChart = [
    {
      data: mainChartData?.map(item => (item === 0 ? null : item)),
      color: "#108554",
      negativeColor: "#E35050"
    }
  ];

  const smallChartValue = sensitiveChangeData ? sensitiveChangeData / 10 : 0;
  let scaleFakeValue = 0;

  if (smallChartValue < 0) {
    scaleFakeValue = smallChartValue < min ? min : smallChartValue;
  } else {
    scaleFakeValue = smallChartValue > max ? max : smallChartValue;
  }

  const seriesSmallChart = [
    {
      data: [setMinChartRange(scaleFakeValue)],
      color: "#108554",
      negativeColor: "#E35050"
    }
  ];

  /* @ts-ignore */
  // function mouseOnBand(e) {
  // if (e.type === "mouseover") {
  // chartRef?.current?.chart?.xAxis[0].update({
  //   plotBands: [{ from: 0, to: 99999, color: "rgba(255,0,0,0.5)" }]
  // });
  /* @ts-ignore */
  // this.svgElem.attr({ fill: "rgba(255, 255, 255, 0.05)" });
  // } else {
  /* @ts-ignore */
  // this.svgElem.attr({ fill: this.options.color });
  //   }
  // }

  const optionsMainChart = getZeroColumnChartOption({ series: seriesMainChart, categories, min, max, height: 480 });
  const optionsMainChart2 = {
    ...optionsMainChart,
    chart: {
      ...optionsMainChart?.chart,
      zooming: {
        enabled: false
      }
    },
    xAxis: {
      ...optionsMainChart.xAxis,
      gridLineWidth: 0,
      lineWidth: 1,
      lineColor: "#858585",
      plotBands:
        Number(mainChartData?.length) > 1 &&
        mainChartData?.map((_, index) => ({
          color: "rgba(61, 61, 61, 0.3)",
          from: index ? index - 0.45 : -1,
          to: index + 0.45,
          states: {
            hover: {
              brightness: -0.3 // darken
            }
          }
          // events: {
          //   mouseover: mouseOnBand,
          //   mouseout: mouseOnBand
          // }
        }))
    },
    yAxis: {
      ...optionsMainChart.yAxis,
      // gridLineColor: "#343438",
      gridLineColor: "#545454",
      gridLineDashStyle: "longdash",
      plotLines: [
        {
          color: "#858585",
          width: 1,
          value: 0,
          zIndex: 5
        }
      ]
    }
  };

  const optionsSmallChart = getZeroSingleColumnChartOption({
    series: seriesSmallChart,
    labelValue: smallChartValue,
    min,
    max
  });

  return (
    <div className="flex flex-col grow">
      <div className="flex justify-between">
        <h5 className="text-sm font-bold leading-6">حساسیت نرخ بهره</h5>
        <span onClick={closeUserModal} className="w-6 h-6 flex justify-center items-center cursor-pointer">
          <CloseIcon width={13.5} height={13.5} className="cursor-pointer" />
        </span>
      </div>
      <div className="flex h-full text-10 mt-3 gap-[2px]">
        <div className="flex flex-col w-[96px] p-2 bg-dark_black7 rounded-r">
          <h6 className="text-center mb-[6px]">متغیر دلخواه</h6>
          <div className="relative grow -mb-[25px]">
            <div
              className={twMerge(
                "absolute left-[-11px] top-0 h-full w-[100px] flex",
                smallChartValue >= 0 ? "flex-col" : "flex-col-reverse"
              )}
            >
              {sensitiveData && <ColumnChart showResetZoom={false} options={optionsSmallChart} className="h-full" />}
            </div>
          </div>
          <SensitivitySearch onChangeSensitivity={onChangeSensitivity} />
        </div>

        <div className="flex flex-col grow justify-center text-center p-2 rounded-l-lg bg-dark_black7">
          <h5>مفروضات تحلیل حساسیت نرخ بهره</h5>
          <div className="flex flex-col grow">
            <div className="flex grow">
              <div className="relative grow top-[10px]">
                {sensitiveData && <ColumnChart showResetZoom={false} options={optionsMainChart2} />}
              </div>
              <div className="w-6 h-full pb-[27px]">
                <div className="flex justify-center items-center rounded-bl rounded-tl w-full h-full text-xs bg-dark_black text-white">
                  <span className="absolute rotate-90 whitespace-nowrap">تغییرات ارزش پرتفوی اوراق</span>
                </div>
              </div>
            </div>
            <span className="flex justify-center text-xs font-normal py-1 rounded-b ml-[81px] bg-dark_black">
              مفروضات تغییرات نرخ بهره
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SensitivityChart;
