"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import { useGetFundConvexityHistoryChart } from "@/queries/FundChartsAPI";
import HighChart from "@/components/Molecules/highChart";
import {
  getSeriesOfInterestRiskFormat,
  getSummaryInterestOptions
} from "@/app/(client)/details/[id]/components//cards/util";
import Loading from "@/components/Atoms/loading";
import { IDetailChartProps } from "../type";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function ConvexityChart({ id }: IDetailChartProps) {
  const { data: raw, isLoading } = useGetFundConvexityHistoryChart({ fundId: id });
  const { data } = raw || {};

  const { graphPoints = [], yearAverage = 0 } = data || {};

  const colors = ["#8871BA"];
  const summary = getSummaryInterestOptions(yearAverage, 0, colors);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        field1: {
          id: "convexity",
          title: "تحدب پرتفو"
        },
        colors
      }),
    [data]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default ConvexityChart;
