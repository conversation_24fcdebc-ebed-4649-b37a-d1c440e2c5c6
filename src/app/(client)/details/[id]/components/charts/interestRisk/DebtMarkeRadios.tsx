import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import RadioGroup from "@/components/Molecules/radioGroup/RadioGroup";
import { IRadioButtonItem } from "@/components/Molecules/radioGroup/type";
import { FUND_INTEREST, FUND_LABELS } from "@/queries/fundsAPI/types";
import { IDebtMarketRadiosProps } from "./types";

function DebtMarketRadios(props: IDebtMarketRadiosProps) {
  const { type, onCardClick, onBackClick } = props;

  const radioItems = [
    {
      id: FUND_INTEREST.WEIGHTED_AVERAGE,
      label: FUND_LABELS[FUND_INTEREST.WEIGHTED_AVERAGE],
      checked: type === FUND_INTEREST.WEIGHTED_AVERAGE.toString()
    },
    {
      id: FUND_INTEREST.DURATION_COUPON,
      label: FUND_LABELS[FUND_INTEREST.DURATION_COUPON],
      checked: type === FUND_INTEREST.DURATION_COUPON.toString()
    },
    {
      id: FUND_INTEREST.DURATION_ZERO_COUPON,
      label: FUND_LABELS[FUND_INTEREST.DURATION_ZERO_COUPON],
      checked: type === FUND_INTEREST.DURATION_ZERO_COUPON.toString()
    }
  ];

  const switchRadio = (value: IRadioButtonItem) => {
    onCardClick(value.id.toString());
  };

  return (
    <div className="flex items-center justify-between pt-3 pb-3">
      <div className="flex gap-2">
        <ArrowRightIcon
          onClick={onBackClick}
          width={20}
          height={20}
          className="cursor-pointer"
          aria-label="back button"
        />
        اطلاعات بازار بدهی
      </div>
      <div className="flex items-center gap-8">
        <div>
          <RadioGroup items={radioItems} onSwitch={switchRadio} size="medium" className="flex gap-2" />
        </div>
        <span className=" text-xs text-white ">بازه زمانی: سه ساله</span>
      </div>
    </div>
  );
}

export default DebtMarketRadios;
