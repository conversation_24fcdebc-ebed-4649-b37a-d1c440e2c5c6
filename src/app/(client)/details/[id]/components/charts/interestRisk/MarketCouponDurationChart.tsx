"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import { useGetMarketCouponDurationHistoryChart } from "@/queries/FundChartsAPI";
import HighChart from "@/components/Molecules/highChart";
import {
  getSeriesOfInterestRiskFormat,
  getSummaryInterestOptions
} from "@/app/(client)/details/[id]/components//cards/util";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function MarketCouponDurationChart() {
  const { data: raw } = useGetMarketCouponDurationHistoryChart();
  const { data } = raw || {};

  const { graphPoints = [], durationYearAverage = 0, modifiedDurationYearAverage = 0 } = data || {};
  const colors = ["#55C3FF", "#9A89C9"];
  const summary = getSummaryInterestOptions(durationYearAverage, modifiedDurationYearAverage, colors);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        field1: {
          id: "duration",
          title: "دیرش"
        },
        field2: {
          id: "modifiedDuration",
          title: "دیرش تعدیل شده"
        },
        colors
      }),
    [data]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
    </ChartWrapper>
  );
}

export default MarketCouponDurationChart;
