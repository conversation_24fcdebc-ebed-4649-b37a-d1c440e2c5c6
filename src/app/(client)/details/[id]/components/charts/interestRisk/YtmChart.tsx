"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import { useGetFundYtmHistoryChart } from "@/queries/FundChartsAPI";
import HighChart from "@/components/Molecules/highChart";
import {
  getSeriesOfInterestRiskFormat,
  getSummaryInterestOptions
} from "@/app/(client)/details/[id]/components//cards/util";
import { parseAsBoolean, useQueryStates } from "nuqs";
import Loading from "@/components/Atoms/loading";
import { IDetailChartProps } from "../type";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function YtmChart({ id }: IDetailChartProps) {
  const { data: raw, isLoading } = useGetFundYtmHistoryChart({ fundId: id });
  const { data } = raw || {};

  const [queryStates] = useQueryStates({
    isShowMarketValue: parseAsBoolean.withDefault(true)
  });
  const { isShowMarketValue } = queryStates;

  const { graphPoints = [], yearAverage = 0, marketYearAverage = 0 } = data || {};
  const colors = ["#8871BA", "#F4F4F4"];
  const summary = getSummaryInterestOptions(yearAverage, isShowMarketValue ? marketYearAverage : undefined, colors);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        checked: !!isShowMarketValue,
        field1: {
          id: "ytm",
          title: "پرتفو"
        },
        field2: {
          id: "marketYtm",
          title: "بازار"
        },
        hasPercent: true,
        colors
      }),
    [data, isShowMarketValue]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} hasPercent />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default YtmChart;
