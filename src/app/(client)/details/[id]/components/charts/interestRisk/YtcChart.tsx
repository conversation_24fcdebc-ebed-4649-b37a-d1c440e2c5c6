"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import { useGetFundYtcHistoryChart } from "@/queries/FundChartsAPI";
import HighChart from "@/components/Molecules/highChart";
import {
  getSeriesOfInterestRiskFormat,
  getSummaryInterestOptions
} from "@/app/(client)/details/[id]/components//cards/util";
import { IDetailChartProps } from "../type";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function YtcChart({ id }: IDetailChartProps) {
  const { data: raw } = useGetFundYtcHistoryChart(id);
  const { data } = raw || {};

  const { graphPoints = [], yearAverage = 0 } = data || {};
  const summary = getSummaryInterestOptions(yearAverage, 0);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        field1: {
          id: "ytc",
          title: "YTC پرتفو"
        }
      }),
    [data]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
    </ChartWrapper>
  );
}

export default YtcChart;
