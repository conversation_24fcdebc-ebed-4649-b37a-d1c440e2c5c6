"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import {
  getSeriesOfMarketRiskFormat,
  getSummaryMarketRisksOptions
} from "@/app/(client)/details/[id]/components/cards/util";
import HighChart from "@/components/Molecules/highChart";
import { useGetFundRiskValueListQuery } from "@/queries/FundChartsAPI";
import { useMemo } from "react";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";
import { IDetailChartProps } from "../type";

function RiskValueChart({ id, isShowRetrospect }: IDetailChartProps) {
  const { data: raw } = useGetFundRiskValueListQuery(id);
  const { data } = raw || {};

  const { graphPoints = [], forwardLookingYearAverage = 0, retrospectiveYearAverage = 0 } = data || {};
  const summary = getSummaryMarketRisksOptions(forwardLookingYearAverage, retrospectiveYearAverage, isShowRetrospect);

  const options = useMemo(
    () =>
      getSeriesOfMarketRiskFormat({
        graphPoints,
        checked: isShowRetrospect || false,
        futureField: "forwardLookingExpectedReturn",
        pastField: "retrospectiveExpectedReturn"
      }),
    [data, isShowRetrospect]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
    </ChartWrapper>
  );
}

export default RiskValueChart;
