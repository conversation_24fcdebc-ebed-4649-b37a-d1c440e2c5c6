"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import {
  getSeriesOfMarketRiskFormat,
  getSummaryMarketRisksOptions
} from "@/app/(client)/details/[id]/components/cards/util";
import Loading from "@/components/Atoms/loading";
import HighChart from "@/components/Molecules/highChart";
import { useGetFundDeviationListQuery } from "@/queries/FundChartsAPI";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { useMemo } from "react";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";
import { IDetailChartProps } from "../type";

function DeviationChart({ id, ...restProps }: IDetailChartProps) {
  const { data: raw, isLoading } = useGetFundDeviationListQuery({ fundId: id });
  const { data } = raw || {};
  const { graphPoints = [], forwardLookingYearAverage = 0, retrospectiveYearAverage = 0 } = data || {};

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { enable_MarketRisk_Tab_Backward_Looking } = useModulesStore();

  const isShowRetrospect = (restProps?.isShowRetrospect || false) && enable_MarketRisk_Tab_Backward_Looking;
  const summary = getSummaryMarketRisksOptions(forwardLookingYearAverage, retrospectiveYearAverage, isShowRetrospect);

  const options = useMemo(
    () =>
      getSeriesOfMarketRiskFormat({
        graphPoints: graphPoints?.map(item => ({
          ...item,
          forwardLookingStandardDeviation: Number(item?.forwardLookingStandardDeviation?.toFixed(3)),
          retrospectiveStandardDeviation: Number(item?.retrospectiveStandardDeviation?.toFixed(3))
        })),
        checked: isShowRetrospect,
        futureField: "forwardLookingStandardDeviation",
        pastField: "retrospectiveStandardDeviation"
      }),
    [data, isShowRetrospect]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default DeviationChart;
