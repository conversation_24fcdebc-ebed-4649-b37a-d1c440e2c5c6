"use client";

/* eslint-disable react-hooks/exhaustive-deps */
import HighChart from "@/components/Molecules/highChart";
import { useGetFundEfficiencyListQuery } from "@/queries/FundChartsAPI";
import { parseAsBoolean, useQueryStates } from "nuqs";
import { useMemo } from "react";

import {
  efficiencyColors,
  getSeriesOfEfficiencyMarketRiskFormat,
  getSummaryMarketEfficiencyOptions
} from "@/app/(client)/details/[id]/components/cards/util";

import Loading from "@/components/Atoms/loading";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";
import { IEfficiencyChartProps } from "../type";

function EfficiencyChart(props: IEfficiencyChartProps) {
  const { id } = props;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { enable_MarketRisk_Tab_Forward_Looking_ActualReturn, enable_MarketRisk_Tab_Backward_Looking } =
    useModulesStore();

  const [queryStates] = useQueryStates({
    isShowExpected: parseAsBoolean.withDefault(true),
    isShowReal: parseAsBoolean.withDefault(true)
  });

  const { isShowExpected } = queryStates;
  const isShowReal = queryStates?.isShowReal && enable_MarketRisk_Tab_Forward_Looking_ActualReturn;
  const isShowRetrospect = (props?.isShowRetrospect || false) && enable_MarketRisk_Tab_Backward_Looking;

  const { data: raw, isLoading } = useGetFundEfficiencyListQuery({ fundId: id });
  const { data } = raw || {};

  const {
    graphPoints = [],
    forwardLookingYearAverage = 0,
    retrospectiveYearAverage = 0,
    simpleYearAverage = 0
  } = data || {};

  const summary = getSummaryMarketEfficiencyOptions({
    future: forwardLookingYearAverage,
    past: retrospectiveYearAverage,
    real: simpleYearAverage,
    isShowRetrospect,
    isShowExpected,
    isShowReal
  });

  const options = useMemo(
    () =>
      getSeriesOfEfficiencyMarketRiskFormat({
        graphPoints,
        isShowRetrospect,
        isShowExpected,
        isShowReal,
        futureField: "forwardLookingExpectedReturn",
        pastField: "retrospectiveExpectedReturn",
        realField: "simpleReturn",
        hasPercent: true,
        colorsArg: [efficiencyColors.future, efficiencyColors.past, efficiencyColors.real]
      }),
    [data, isShowRetrospect, isShowExpected, isShowReal]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} hasPercent />}>
      {raw && <HighChart options={options} showTunedPeriod />}

      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default EfficiencyChart;
