"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import {
  getSeriesOfMarketRiskFormat,
  getSummaryMarketRisksOptions
} from "@/app/(client)/details/[id]/components/cards/util";
import Loading from "@/components/Atoms/loading";
import HighChart from "@/components/Molecules/highChart";
import { useGetFundAlphaListQuery } from "@/queries/FundChartsAPI";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { useMemo } from "react";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";
import { IDetailChartProps } from "../type";

function AlphaChart({ id, ...restProps }: IDetailChartProps) {
  const { data: raw, isLoading } = useGetFundAlphaListQuery({ fundId: id });
  const { data } = raw || {};

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { enable_MarketRisk_Tab_Backward_Looking } = useModulesStore();
  const isShowRetrospect = (restProps?.isShowRetrospect || false) && enable_MarketRisk_Tab_Backward_Looking;

  const { graphPoints = [], forwardLookingYearAverage = 0, retrospectiveYearAverage = 0 } = data || {};
  const summary = getSummaryMarketRisksOptions(forwardLookingYearAverage, retrospectiveYearAverage, isShowRetrospect);

  const options = useMemo(
    () =>
      getSeriesOfMarketRiskFormat({
        graphPoints: graphPoints?.map(item => ({
          ...item,
          forwardLookingJensensAlpha: Number(item?.forwardLookingModifiedJensensAlpha?.toFixed(2)),
          retrospectiveJensensAlpha: Number(item?.retrospectiveModifiedJensensAlpha?.toFixed(2))
        })),
        checked: isShowRetrospect || false,
        futureField: "forwardLookingJensensAlpha",
        pastField: "retrospectiveJensensAlpha"
      }),
    [data, isShowRetrospect]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default AlphaChart;
