import { Tooltip } from "react-tooltip";
import InfoIcon from "@/assets/icons/info.svg";
import InfoIconFill from "@/assets/icons/info-fill.svg";
import { twMerge } from "tailwind-merge";

type TTooltipCard = {
  needleColor?: string;
  color?: string;
  name?: string;
  y?: string | number | null | JSX.Element;
  textColor?: string;
};

function InfoCardTooltip(props: {
  id: string;
  data: TTooltipCard[];
  unit?: string;
  showAlert?: boolean;
  isRiskValue?: boolean;
}) {
  const { id, data = [], unit = "", showAlert, isRiskValue } = props;
  const isEmpty = data.length === 0;

  return (
    <>
      {showAlert ? (
        <InfoIconFill
          data-tooltip-id={id}
          width={16}
          height={16}
          className={twMerge(" cursor-default outline-none text-white")}
        />
      ) : (
        <InfoIcon
          data-tooltip-id={id}
          width={16}
          height={16}
          className={twMerge(" cursor-default outline-none text-white")}
        />
      )}

      <Tooltip
        id={id}
        place="left-start"
        float
        className="!p-3 !border !border-dark_black8 !bg-dark_black9 !opacity-100 !rounded-lg min-w-[144px] !mx-2 !z-[99999999]"
        classNameArrow="mt-2"
      >
        <div className="flex flex-col gap-2 leading-4 z-20">
          {isEmpty && <span className="text-center leading-4 text-10 text-borderLightGray">اطلاعات در دسترس نیست</span>}
          {data.map(i => (
            <div
              className={twMerge(
                "flex justify-between gap-5 ",
                i.color === "transparent" && "text-secondaryText",
                !isRiskValue && i.needleColor && "border-t border-t-dark_black8 pt-2"
              )}
            >
              <div className="flex gap-2 text-xs items-center">
                {i.color && <span className="w-3 h-3 rounded-sm" style={{ background: i.color }} />}

                {i.needleColor && (
                  <div>
                    <div style={{ background: i.needleColor }} className="w-[5px] h-[5px] rounded-full" />
                    <div
                      style={{ background: i.needleColor }}
                      className="w-px h-[7px] rounded-2.5xl relative right-0.5 bottom-px"
                    />
                  </div>
                )}

                <span style={{ color: i.textColor }}>{i.name}</span>
              </div>
              <div className="flex gap-1">
                <span className="ltr flex">{i.y}</span>
                {unit && <span className="text-xs leading-3 text-textSecondary">{unit}</span>}
              </div>
            </div>
          ))}
        </div>
      </Tooltip>
    </>
  );
}

export default InfoCardTooltip;
