import { IZone } from "@/components/Atoms/circleGauge/types";

/* eslint-disable @typescript-eslint/naming-convention */
export enum FUND_TABS {
  MARKET_RISK = 1,
  INTEREST_RISK = 2,
  ADAPTIVE_RISK = 3,
  OPERATIONAL_RISK = 4,
  FUND_ASSETS_LIQUID = 5,
  LIQUIDITY_RISK = 6,
  FOCUS_RISK = 7
}

export enum FUND_SUB_TABS {
  EVALUATION = 1,
  RISK_MANAGEMENT = 2
}

export enum FOCUS_SUB_TABS {
  SHAREHOLDERS = 1,
  ASSETS = 2
}

export enum EFFICIENCY_GAUGE {
  EXPECTED = "isShowExpected",
  REAL = "isShowReal"
}

// Operational Risks - Risks by risk manager
export const RISKS_BY_MANAGER = {
  STOCK_MATTERS_RISK: "امور سهام",
  ASSEMBLIES_COMPANIES_AFFAIR_RISK: "امور مجامع و شرکت ها",
  MARKET_MAKING_RISK: "بازار گردانی",
  PLANNING_STRATEGY_RISK: "برنامه ریزی و راهبرد",
  JURIDICAL_RISK: "حقوقی",
  INVESTMENT_RISK: "سرمایه گذاری",
  IT_RAND_RISK: "فناوری اطلاعات  ",
  FINANCIAL_َADMINISTRATIVE_RISK: "مالی و اداری"
};

export interface IFundChartFilter {
  isCheckedRetrospect: boolean;
  className?: string;
  onChange: () => void;
}

export interface IGaugeMarketCardProps {
  id: string;
  title: string;
  desc: string;
  structor: IZone[];
  past?: number;
  future?: number;
  pastJensens?: number;
  futureJensens?: number;
  isSelected: boolean;
  isShowRetrospect: boolean;
  onCardClick: (id: string) => void;
  compareValue?: number[];
  compareValueColor?: string[];
  fundId: string;
}

export interface IGaugeEfficiencyCardProps {
  id: string;
  title: string;
  desc: string;
  structor: IZone[];
  past?: number;
  future?: number;
  real?: number;
  isSelected: boolean;
  isShowRetrospect: boolean;
  onCardClick: (id: string) => void;
  compareValue?: number[];
  compareValueColor?: string[];
  fundId: string;
  isYtm?: boolean;
}

export interface IGaugeInterestCardProps {
  id: string;
  type?: string;
  title: string;
  desc: string;
  structor: IZone[];
  value?: number | null;
  market?: number | null;
  isSelected: boolean;
  onCardClick: (id: string) => void;
  showAlert?: boolean;
  compareValue?: number[] | null;
  compareValueColor?: string[];
  hasPercentInCompare?: boolean;
  isYtm?: boolean;
}

export interface IDeviationMarketCardProps {
  id: string;
  title: string;
  desc: string;
  pastValue?: number;
  futureValue?: number;
  isSelected: boolean;
  onCardClick: (id: string) => void;
  isShowRetrospect: boolean;
  compareValue?: number;
  compareListCount?: number;
}

export type TSummaryMarketRiskOptions = {
  future: number;
  past: number;
  real: number;
  isShowRetrospect?: boolean;
  isShowExpected?: boolean;
  isShowReal?: boolean;
};
