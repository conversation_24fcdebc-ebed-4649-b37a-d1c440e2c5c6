/* eslint-disable @typescript-eslint/naming-convention */

import useModulesStore from "@/store/modulesStore/ModulesStore";
import { toFixed, toFixedNumber } from "@/utils/helpers";
import { twMerge } from "tailwind-merge";
import InfoCardTooltip from "./InfoCardTooltip";
import { IDeviationMarketCardProps } from "./type";

function DeviationMarketCard(props: IDeviationMarketCardProps) {
  const { id, title, desc, pastValue, futureValue, isSelected, onCardClick, isShowRetrospect } = props;
  const { enable_MarketRisk_Tab_Backward_Looking } = useModulesStore();

  const futureValueFormat = toFixed(futureValue || 0, 2);
  const pastValueFormat = toFixed(pastValue || 0, 2);

  const tooltipData: { needleColor?: string; color?: string; name?: string; y?: number | string }[] = [
    { color: "#26D5C0", name: "آینده‌نگر", y: futureValueFormat },
    ...(enable_MarketRisk_Tab_Backward_Looking ? [{ color: "#C7DA41", name: "گذشته‌نگر", y: pastValueFormat }] : [])
  ];

  if (props?.compareValue) {
    tooltipData.push({
      needleColor: "#26D5C0",
      name: `میانگین ${props?.compareListCount} صندوق`,
      y: toFixedNumber(props?.compareValue || 0, 2)
    });
  }

  const onClickHandle = () => onCardClick(id);

  return (
    <div
      onClick={onClickHandle}
      className={twMerge(
        "flex flex-col h-full p-[11px] pr-14px rounded-lg justify-between cursor-pointer bg-dark_black border border-cardBackground hover:border-borderBorderAndDivider",
        isSelected && "border-2 !border-accept_green08"
      )}
      data-test={`card-${id}`}
    >
      <div className="flex flex-col gap-2">
        <div className="flex justify-between">
          <h5>{title}</h5>
          <InfoCardTooltip id={id} data={tooltipData} unit="" />
        </div>
        <span className="text-xs leading-4">{desc}</span>
      </div>

      <div className="flex flex-col justify-center items-center text-xs leading-8 rounded bg-dark_black9 -mr-[3px]">
        <div className="flex w-full justify-between pl-3 pr-3 text-withCouponCyan">
          <span>آینده نگر</span>
          <span>{futureValueFormat}</span>
        </div>

        {enable_MarketRisk_Tab_Backward_Looking && isShowRetrospect && (
          <div className="flex w-full justify-between pl-3 pr-3 text-withCouponYellow">
            <span>گذشته نگر</span>
            <span>{pastValueFormat}</span>
          </div>
        )}
      </div>
    </div>
  );
}

export default DeviationMarketCard;
