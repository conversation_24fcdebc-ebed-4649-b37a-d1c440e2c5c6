/* eslint-disable jsx-a11y/heading-has-content */
/* eslint-disable react-hooks/exhaustive-deps */
import { HeatMap } from "@/components/Molecules/heatMap";
import { THeatRow } from "@/components/Molecules/heatMap/type";
import { HeatChartTable } from "@/components/Molecules/heatMap/util";
import { useGetFundHeatMapEvaluationListQuery } from "@/queries/fundsAPI";
import { useEffect, useMemo } from "react";
import TooltipHeatMapEvaluationRisks from "../charts/chartLayouts/TooltipHeatMapEvaluationRisks";

function HeatChartEvaluation({ fundId, refresh }: { fundId: string; refresh: number }) {
  const { data: fundData, refetch } = useGetFundHeatMapEvaluationListQuery(fundId);
  const { data: fundRows = [] } = fundData || {};

  const heatRows: THeatRow[] = useMemo(
    () =>
      HeatChartTable.map(i => {
        const count = fundRows.filter(f => f.probability === i.x && f.impactRate === i.y).length;
        return count ? { ...i, value: count } : i;
      }),
    [fundRows]
  );

  useEffect(() => {
    if (refresh) {
      refetch();
    }
  }, [refresh]);

  const tid = `HeatChartEvaluation-${fundId}`;

  return (
    <div className="grow flex flex-col items-center justify-center mx-auto px-2 pb-2 pt-4 w-[476px] rounded-lg bg-backgroundCardBackground max-h-[272px] 2xl:max-h-full ">
      <span className="text-sm font-bold text-[#F4F4F4] text-right self-start">وضعیت ریسک سازمان</span>
      {/* <h3 className="self-start text-sm leading-6 font-bold text-borderLightGray" /> */}
      <div className="flex grow items-center justify-center ">
        <HeatMap id={tid} rows={heatRows} />
        <TooltipHeatMapEvaluationRisks fundId={fundId} tooltipId={tid} />
      </div>
    </div>
  );
}

export default HeatChartEvaluation;
