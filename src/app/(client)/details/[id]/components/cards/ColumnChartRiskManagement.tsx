/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from "react";
import { ColumnChart } from "@/components/Molecules/columnChart";
import { useGetFundColumnChartRiskManagementQuery } from "@/queries/fundsAPI";
import { ORGANIZATION_STATUS_LABELS } from "@/components/Molecules/heatMap/util";
import { getColumnChartOption } from "@/components/Molecules/columnChart/util";
import { columnChartColorsDark, columnChartColorsLight } from "./util";

function ColumnChartRiskManagement({ fundId, refresh }: { fundId: string; refresh: number }) {
  const { data: tooltipQuery, isLoading, refetch } = useGetFundColumnChartRiskManagementQuery(fundId);

  useEffect(() => {
    if (refresh) {
      refetch();
    }
  }, [refresh]);

  const {
    previousCriticalRiskPercent = 0,
    criticalRiskPercent = 0,
    previousHighRiskPercent = 0,
    highRiskPercent = 0,
    previousMediumRiskPercent = 0,
    mediumRiskPercent = 0,
    previousLowRiskPercent = 0,
    lowRiskPercent = 0
  } = tooltipQuery?.data || {};

  const categories = [
    ORGANIZATION_STATUS_LABELS.LOW,
    ORGANIZATION_STATUS_LABELS.MODERATE,
    ORGANIZATION_STATUS_LABELS.HIGH,
    ORGANIZATION_STATUS_LABELS.CRITICAL
  ];

  const series = [
    {
      data: [
        { y: Math.round(previousLowRiskPercent), color: columnChartColorsLight[0] },
        { y: Math.round(previousMediumRiskPercent), color: columnChartColorsLight[1] },
        { y: Math.round(previousHighRiskPercent), color: columnChartColorsLight[2] },
        { y: Math.round(previousCriticalRiskPercent), color: columnChartColorsLight[3] }
      ]
    },
    {
      data: [
        { y: Math.round(lowRiskPercent), color: columnChartColorsDark[0] },
        { y: Math.round(mediumRiskPercent), color: columnChartColorsDark[1] },
        { y: Math.round(highRiskPercent), color: columnChartColorsDark[2] },
        { y: Math.round(criticalRiskPercent), color: columnChartColorsDark[3] }
      ]
    }
  ];

  const options = getColumnChartOption({
    series,
    categories,
    tooltipEnabled: false,
    groupPadding: 0.05,
    x: -1,
    stacking: false
  });

  return (
    <div className="flex flex-col grow h-full rounded-lg pt-3 bg-backgroundCardBackground">
      <h4 className="text-sm leading-6 font-bold mb-3 pl-2 pr-3 text-borderLightGray">ریسک‌ها براساس شدت</h4>
      {!isLoading && <ColumnChart options={options} className="grow p-[2px]" />}
    </div>
  );
}

export default ColumnChartRiskManagement;
