import DiagramIcon from "@/assets/icons/diagram.svg";
import { useGetMarketInterestRiskValuesQuery } from "@/queries/fundsAPI";
import { toFixed, toFixedInPercent } from "@/utils/helpers";
import { isNullOrEmpty } from "./util";

function InterestRiskMarketCard(props: { onCardClick: () => void }) {
  const { onCardClick } = props;

  const { data } = useGetMarketInterestRiskValuesQuery();
  const { data: riskValues } = data || {};

  const {
    couponBondsValueWeightedDuration,
    couponBondsValueWeightedModifiedDuration,
    riskFreeRate,
    zeroBondsValueWeightedDuration,
    zeroBondsValueWeightedModifiedDuration
  } = riskValues || {};

  return (
    <div
      onClick={onCardClick}
      className="cursor-pointer h-full p-[11px] flex flex-col justify-between rounded-lg border border-backgroundCardBackground hover:border-borderBorderAndDivider bg-dark_black text-borderLightGray"
      aria-label="Interest Risk Market Card"
    >
      <div className="flex flex-col">
        <div className="flex justify-between">
          <h5 className="text-base leading-[25px]">اطلاعات بازار بدهی</h5>
          <DiagramIcon width="24" height="24" />
        </div>
        <div className="flex items-center gap-2 mt-[6px] text-xs leading-4 rounded-[4px] text-textSecondary">
          <span>نرخ بهره بدون ریسک:</span>
          <span className="text-xs ltr">
            {!isNullOrEmpty(riskFreeRate) ? `${toFixedInPercent(riskFreeRate, 2)}` : "---"}
          </span>
        </div>
      </div>

      <div className="flex gap-3 text-xs">
        <div className="flex flex-col grow basis-0 px-3 rounded bg-dark_black9">
          <h6 className="leading-10 text-secondaryText">دیرش اوراق کوپن ‌دار</h6>
          <div className="flex justify-between leading-9 text-light_blue2">
            <span>مک کالی</span>
            <span>
              {!isNullOrEmpty(couponBondsValueWeightedDuration) ? toFixed(couponBondsValueWeightedDuration) : "---"}
            </span>
          </div>
          <div className="flex justify-between relative top-[-3px] leading-9 text-accept_green07">
            <span>تعدیل شده</span>
            <span>
              {!isNullOrEmpty(couponBondsValueWeightedModifiedDuration)
                ? toFixed(couponBondsValueWeightedModifiedDuration)
                : "---"}
            </span>
          </div>
        </div>

        <div className="flex flex-col grow basis-0 px-3 rounded bg-dark_black9">
          <h6 className="leading-10 text-secondaryText">دیرش اوراق بدون کوپن</h6>
          <div className="flex justify-between leading-9 text-light_blue2">
            <span>مک کالی</span>
            <span>
              {!isNullOrEmpty(zeroBondsValueWeightedDuration) ? toFixed(zeroBondsValueWeightedDuration) : "---"}
            </span>
          </div>
          <div className="flex justify-between relative top-[-3px] leading-9 text-accept_green07">
            <span>تعدیل شده</span>
            <span>
              {!isNullOrEmpty(zeroBondsValueWeightedModifiedDuration)
                ? toFixed(zeroBondsValueWeightedModifiedDuration)
                : "---"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default InterestRiskMarketCard;
