/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useMemo } from "react";
import { twMerge } from "tailwind-merge";
import { HeatMap } from "@/components/Molecules/heatMap";
import { useSuccessPercentOfOrganizationQuery, useGetFundHeatMapManagementRiskListQuery } from "@/queries/fundsAPI";
import { THeatRow } from "@/components/Molecules/heatMap/type";
import { HeatChartTable } from "@/components/Molecules/heatMap/util";
import { toFixed } from "@/utils/helpers";
import TooltipHeatMapRiskManagement from "../charts/chartLayouts/TooltipHeatMapRiskManagement";
import { getColorByValue } from "./util";

function HeatChartRiskManagement({ fundId, refresh }: { fundId: string; refresh: number }) {
  const { data: fundData, refetch: refetchRisks } = useGetFundHeatMapManagementRiskListQuery(fundId);
  const { data: fundRows = [] } = fundData || {};

  const { data: successPercentData, refetch: refetchSuccessPercent } = useSuccessPercentOfOrganizationQuery(fundId);
  const { data: successPercent } = successPercentData || {};

  useEffect(() => {
    if (refresh) {
      refetchRisks();
      refetchSuccessPercent();
    }
  }, [refresh]);

  const tid = `heatChartRiskManagement-${fundId}`;
  const isEmpty = fundRows.length === 0;

  const heatRows: THeatRow[] = useMemo(
    () =>
      HeatChartTable.map(i => {
        const item = fundRows.find(f => f.probability === i.x && f.impactRate === i.y);
        const { countAfterAction = 0, countBeforeAction = 0 } = item || {};

        if (item) {
          return { ...i, value: { current: countAfterAction, previous: countBeforeAction } };
        }

        return i;
      }),
    [fundRows]
  );

  return (
    <div className="grow flex flex-col items-center justify-center mx-auto p-2  w-[476px] rounded-lg bg-backgroundCardBackground">
      <div className="flex justify-between w-full text-borderLightGray">
        <h3 className="self-start text-sm leading-6 font-bold">وضعیت ریسک سازمان</h3>
        <span className="flex text-sm leading-6 rounded px-2 bg-dark_black9">
          درصد موفقیت:
          <span className={twMerge("ltr mr-1", getColorByValue(successPercent))}>
            {successPercent !== undefined ? <span>% {toFixed(successPercent)}</span> : "---"}
          </span>
        </span>
      </div>
      <div className="flex grow items-center justify-center">
        <HeatMap id={tid} rows={heatRows} isEmpty={isEmpty} />
        <TooltipHeatMapRiskManagement fundId={fundId} tooltipId={tid} refresh={refresh} />
      </div>
    </div>
  );
}

export default HeatChartRiskManagement;
