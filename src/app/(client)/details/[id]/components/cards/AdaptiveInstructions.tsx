import { useGetFundLastEvaluationQuery, useGetFundTotalEvaluationQuery } from "@/queries/fundsAPI";
import AdaptiveInstructionHeading from "./AdaptiveInstructionHeading";
import AdaptiveInstructionRow from "./AdaptiveInstructionRow";

function AdaptiveInstructions({ fundId }: { fundId: string }) {
  const { data: lastQuery } = useGetFundLastEvaluationQuery(fundId);
  const { data: last } = lastQuery || {};

  const { data: totalQuery } = useGetFundTotalEvaluationQuery(fundId);
  const { data: total } = totalQuery || {};

  const lastData = [
    { id: 1, title: "درصد رعایت", value: last?.compliancePercentage },
    { id: 3, title: "رعایت شده", value: last?.compliance },
    { id: 4, title: "رعایت نشده", value: last?.nonCompliance },
    { id: 2, title: "موضوعیت نداشته", value: last?.noRelevance }
  ];

  const totalData = [
    { id: 1, title: "درصد رعایت", value: total?.compliancePercentage },
    { id: 3, title: "رعایت شده", value: total?.compliance },
    { id: 4, title: "رعایت نشده", value: total?.nonCompliance },
    { id: 2, title: "موضوعیت نداشته", value: total?.noRelevance }
  ];

  return (
    <div className="flex flex-col w-[194px]">
      <div className="flex flex-col gap-5 grow p-2   rounded-lg bg-backgroundCardBackground">
        <div aria-roledescription="row" className="mt-[1px]">
          <AdaptiveInstructionHeading title="ارزیابی جاری" date={last?.checkpointDate} />
          <div className="flex flex-col gap-1">
            {lastData.map((i, key) => (
              <AdaptiveInstructionRow key={i.id} title={i.title} value={i.value} isFirst={key === 0} />
            ))}
          </div>
        </div>

        <div aria-roledescription="row" className="mt-[21px]">
          <AdaptiveInstructionHeading title="ارزیابی کلی" />
          <div className="flex flex-col gap-1">
            {totalData.map((i, key) => (
              <AdaptiveInstructionRow key={i.id} title={i.title} value={i.value} isFirst={key === 0} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdaptiveInstructions;
