import CircleGauge from "@/components/Atoms/circleGauge";
import { IZone } from "@/components/Atoms/circleGauge/types";
import FundCardsSkeleton from "@/components/Atoms/skeleton/Skeleton";
import { useGetPEDataQuery } from "@/queries/fundsAPI";
import { useGetAllFundsFrontQuery } from "@/queries/fundsApiFront";
import { toDecimals } from "@/utils/helpers";

function PEModal({ fundId }: { fundId: string }) {
  const { data, isLoading } = useGetPEDataQuery(fundId);

  const { data: allFunds, isLoading: allFundIsLoading } = useGetAllFundsFrontQuery();

  const fundViewConfig = allFunds?.data?.find(item => item?.id === fundId)?.fundViewConfig;

  const bondsPriceToEarnings =
    (data?.data?.bondsPriceToEarnings?.orderedRanges
      // eslint-disable-next-line no-unsafe-optional-chaining
      ?.sort((a, b) => a?.minimum || 0 - b?.minimum || 0)
      ?.map((item, index) => ({
        id: index,
        type: item?.levelType,
        level: item?.level,
        min: item?.minimum,
        max: item?.maximum
      })) as unknown as IZone[]) || [];

  const investmentGroupPriceToEarnings =
    (data?.data?.investmentGroupPriceToEarnings?.orderedRanges
      // eslint-disable-next-line no-unsafe-optional-chaining
      ?.sort((a, b) => a?.minimum || 0 - b?.minimum || 0)
      ?.map((item, index) => ({
        id: index,
        type: item?.levelType,
        level: item?.level,
        min: item?.minimum,
        max: item?.maximum
      })) as unknown as IZone[]) || [];

  const sharesPriceToEarnings =
    (data?.data?.sharesPriceToEarnings?.orderedRanges
      // eslint-disable-next-line no-unsafe-optional-chaining
      ?.sort((a, b) => a?.minimum || 0 - b?.minimum || 0)
      ?.map((item, index) => ({
        id: index,
        type: item?.levelType,
        level: item?.level,
        min: item?.minimum,
        max: item?.maximum
      })) as unknown as IZone[]) || [];

  return (
    <div className="h-full pt-0 pb-0 ">
      {isLoading || allFundIsLoading ? (
        <div className="rounded-xl h-[120px] overflow-hidden">
          <FundCardsSkeleton />
        </div>
      ) : (
        <div>
          {/* <div className="flex flex-col h-full justify-between">
            <h5 className="text-16 font-bold">جزئیات P/E</h5>
          </div> */}

          {/* {allFunds &&
            data?.data?.map(item => {
              const fundSelectedFromAll = allFunds?.data?.find(i => i.id === item.fundId);

              if (!fundSelectedFromAll) return undefined;

              return <SingleFundCard fund={item} fundViewConfig={fundSelectedFromAll?.fundViewConfig} />;
            })} */}
          <div className="flex items-center gap-8 mt-3 pt-0 pb-0 ">
            {fundViewConfig?.enable_PricePerEPSForStockPortfolio && (
              <div className="">
                <CircleGauge
                  labelFormatter={item => item?.min?.toFixed(2)?.replace(/\.00$/, "")}
                  lastLabel={
                    investmentGroupPriceToEarnings?.length > 0
                      ? investmentGroupPriceToEarnings
                          ?.sort((a, b) => Number(a?.max) - Number(b?.max))
                          ?.[Number(investmentGroupPriceToEarnings?.length) - 1]?.max?.toFixed(2)
                          ?.replace(/\.00$/, "")
                      : ""
                  }
                  size="medium"
                  data={sharesPriceToEarnings}
                  value={data?.data?.sharesPriceToEarnings?.value}
                  insideCircleClassName="bg-backgroundCardBackground"
                  arrowColors={["#0C82F9"]}
                />

                <div className="flex items-center justify-between px-3 py-2 rounded bg-[#28282C] mt-2">
                  <span className="text-sm">پرتفوی سهامی</span>
                  <span className="text-base font-bold">
                    {data?.data?.sharesPriceToEarnings?.value
                      ? toDecimals(data?.data?.sharesPriceToEarnings?.value)
                      : "---"}
                  </span>
                </div>
              </div>
            )}

            {fundViewConfig?.enable_PricePerEPSForBondPortfolio && (
              <div className="">
                <CircleGauge
                  size="medium"
                  labelFormatter={item => item?.min?.toFixed(2)?.replace(/\.00$/, "")}
                  lastLabel={bondsPriceToEarnings
                    ?.sort((a, b) => Number(a?.max) - Number(b?.max))
                    ?.[Number(bondsPriceToEarnings?.length) - 1]?.max?.toFixed(2)
                    ?.replace(/\.00$/, "")}
                  data={bondsPriceToEarnings}
                  value={data?.data?.bondsPriceToEarnings?.value}
                  insideCircleClassName="bg-backgroundCardBackground"
                  arrowColors={["#B5179E"]}
                />

                <div className="flex items-center justify-between px-3 py-2 rounded bg-[#28282C] mt-2">
                  <span className="text-sm">پرتفوی اوراق</span>
                  <span className="text-base font-bold">
                    {data?.data?.bondsPriceToEarnings?.value
                      ? toDecimals(data?.data?.bondsPriceToEarnings?.value)
                      : "---"}
                  </span>
                </div>
              </div>
            )}
            <div className="">
              <CircleGauge
                size="medium"
                labelFormatter={item => item?.min?.toFixed(2)?.replace(/\.00$/, "")}
                lastLabel={investmentGroupPriceToEarnings
                  ?.sort((a, b) => Number(a?.max) - Number(b?.max))
                  ?.[Number(investmentGroupPriceToEarnings?.length) - 1]?.max?.toFixed(2)
                  ?.replace(/\.00$/, "")}
                data={investmentGroupPriceToEarnings}
                value={data?.data?.investmentGroupPriceToEarnings?.value}
                insideCircleClassName="bg-backgroundCardBackground"
                arrowColors={["#26ECA0"]}
              />

              <div className="flex items-center justify-between px-3 py-2 rounded bg-[#28282C] mt-2">
                <span className="text-sm">گروه سرمایه گذاری</span>
                <span className="text-base font-bold">
                  {data?.data?.investmentGroupPriceToEarnings?.value
                    ? toDecimals(data?.data?.investmentGroupPriceToEarnings?.value)
                    : "---"}
                </span>{" "}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default PEModal;
