import { useGetFundAssetsCompositionQuery, useGetFundIndustriesSharePercentQuery } from "@/queries/fundsApiFront";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { twMerge } from "tailwind-merge";
import AssetPieCard from "./AssetPieCard";
import CompanyInfoBox from "./CompanyInfoBox";
import IndustriesPieCard from "./IndustriesPieCard";
import PECard from "./PECard";

function HeadBoxDetails({ className, fundId }: { className?: string; fundId: string }) {
  const { data: industryQuery, isLoading: isLoadingIndustry } = useGetFundIndustriesSharePercentQuery(fundId);
  const { data: industryData = [] } = industryQuery || {};

  const { data: assetsQuery, isLoading: isLoadingAssets } = useGetFundAssetsCompositionQuery(fundId);
  const { data: assetsData = [] } = assetsQuery || {};

  // zustand hook
  const { enable_FundsIndustriesComposition: isEnabledIndustry, enable_FundAssetsComposition: isEnabledAssets } =
    useModulesStore();

  const isEmptyIndustry = !isLoadingIndustry && industryData?.length === 0;
  const isEmptyAssets = !isLoadingAssets && assetsData?.length === 0;

  return (
    <div
      className={twMerge(
        "grid gap-2",
        isEnabledAssets && isEnabledIndustry ? "grid-cols-[1fr_228px_322px]" : "grid-cols-[1fr_228px_161px]",
        className
      )}
    >
      <div className="">
        <CompanyInfoBox fundId={fundId} />
      </div>

      <div className={twMerge("")}>
        <PECard fundId={fundId} />
      </div>

      <div className="flex justify-between rounded-xl bg-backgroundCardBackground">
        {isEnabledAssets && <AssetPieCard fundId={fundId} isEmpty={isEmptyAssets} />}
        {isEnabledIndustry && <IndustriesPieCard fundId={fundId} isEmpty={isEmptyIndustry} />}
      </div>
    </div>
  );
}

export default HeadBoxDetails;
