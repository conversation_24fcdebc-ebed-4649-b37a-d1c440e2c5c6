/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/naming-convention */
import { useEffect } from "react";
import { StackChart } from "@/components/Molecules/stackChart";
import { useGetFundStackChartEvaluationQuery } from "@/queries/fundsAPI";
import { RISKS_BY_MANAGER } from "./type";

function StackChartEvaluation({ fundId, refresh }: { fundId: string; refresh: number }) {
  const { data: risksByOrgQuery, refetch } = useGetFundStackChartEvaluationQuery(fundId);

  useEffect(() => {
    if (refresh) {
      refetch();
    }
  }, [refresh]);

  const {
    assemblies_Companies_AffairRiskPercent = 0,
    financial_َAministrativeRiskPercent = 0,
    iT_RandDRiskPercent = 0,
    investmentRiskPercent = 0,
    juridicalRiskPercent = 0,
    marketMakingRiskPercent = 0,
    planning_StrategyRiskPercent = 0,
    stockMattersRiskPercent = 0
  } = risksByOrgQuery?.data || {};

  const rows = [
    { title: RISKS_BY_MANAGER.STOCK_MATTERS_RISK, value: stockMattersRiskPercent, color: "#960D59" },
    {
      title: RISKS_BY_MANAGER.ASSEMBLIES_COMPANIES_AFFAIR_RISK,
      value: assemblies_Companies_AffairRiskPercent,
      color: "#B5179E"
    },
    { title: RISKS_BY_MANAGER.MARKET_MAKING_RISK, value: marketMakingRiskPercent, color: "#7209B7" },

    { title: RISKS_BY_MANAGER.PLANNING_STRATEGY_RISK, value: planning_StrategyRiskPercent, color: "#3F37C9" },
    { title: RISKS_BY_MANAGER.JURIDICAL_RISK, value: juridicalRiskPercent, color: "#4361EE" },
    { title: RISKS_BY_MANAGER.INVESTMENT_RISK, value: investmentRiskPercent, color: "#4895EF" },

    { title: RISKS_BY_MANAGER.IT_RAND_RISK, value: iT_RandDRiskPercent, color: "#4CC9F0" },
    {
      title: RISKS_BY_MANAGER.FINANCIAL_َADMINISTRATIVE_RISK,
      value: financial_َAministrativeRiskPercent,
      color: "#888"
    }
  ];

  return (
    <div className="flex flex-col shrink-0 p-3 basis-[234px] gap-2 h-full rounded-lg bg-backgroundCardBackground">
      <h4 className="text-sm leading-6 font-bold mb-1 text-borderLightGray">بر اساس واحد سازمانی</h4>
      <StackChart rows={rows} />
    </div>
  );
}

export default StackChartEvaluation;
