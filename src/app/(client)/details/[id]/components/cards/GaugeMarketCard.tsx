/* eslint-disable @typescript-eslint/naming-convention */

import CircleGauge from "@/components/Atoms/circleGauge";
import { FUND_TYPES } from "@/queries/fundsAPI/types";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { convertIntToCurrency, toFixedNumber, numbersUnitInToman } from "@/utils/helpers";
import { memo } from "react";
import { twMerge } from "tailwind-merge";
import { useGetFundMarketRisksListQuery } from "@/queries/fundsAPI";
import InfoCardTooltip from "./InfoCardTooltip";
import { IGaugeMarketCardProps } from "./type";

type TTooltip = {
  color?: string;
  name: string;
  y: string | number | null | JSX.Element;
  needleColor?: string;
}[];

function GaugeMarketCard(props: IGaugeMarketCardProps) {
  const {
    id,
    title,
    desc,
    structor = [],
    past,
    future,
    pastJensens,
    futureJensens,
    isSelected,
    onCardClick,
    compareValue,
    compareValueColor,
    fundId
  } = props;

  const futureValue = toFixedNumber(future || 0, 3);
  const pastValue = toFixedNumber(past || 0, 3);

  const isEfficiency = id === FUND_TYPES.EFFICIENCY;
  const isRiskValue = id === FUND_TYPES.RISK_VALUE;
  const { enable_MarketRisk_Tab_Backward_Looking, enable_MarketRisk_Tab_Forward_Looking } = useModulesStore();

  const isShowRetrospect = props?.isShowRetrospect && enable_MarketRisk_Tab_Backward_Looking;
  const values = [futureValue, isShowRetrospect ? pastValue : null];

  const { data: fundQuery } = useGetFundMarketRisksListQuery(fundId);
  const { data } = fundQuery || {};

  const findY = (item?: number) => {
    if (!item && item !== 0) return "-";

    if (isRiskValue)
      return (
        <div className="flex">
          <div>{numbersUnitInToman(item || 0)}</div>
        </div>
      );
    if (isEfficiency) return `${toFixedNumber((item || 0) * 100, 2)}٪`;
    return toFixedNumber(item || 0, 2);
  };

  const tooltipData: TTooltip = [
    {
      color: "#26D5C0",
      name: isRiskValue ? "پارامتریک" : "آینده‌نگر",
      y: findY(future)
    }
  ];

  if (futureJensens !== undefined) {
    const futureJensensValue = toFixedNumber(futureJensens || 0, 4);
    tooltipData.push({
      color: "transparent",
      name: "مقدار آلفای جنسن",
      y: isEfficiency ? futureJensensValue * 100 : futureJensensValue
    });
  }

  if (isRiskValue) {
    tooltipData.push({
      color: "#C7DA41",
      name: "تاریخی",
      y: findY(past)
    });
    if (data?.compareListCount) {
      tooltipData.push(
        {
          needleColor: compareValueColor?.[0],
          name: `میانگین ${data.compareListCount}صندوق پارامتریک`,
          y: findY(compareValue?.[0])
        },
        {
          needleColor: compareValueColor?.[1],
          name: `میانگین ${data.compareListCount} صندوق تاریخی`,
          y: findY(compareValue?.[1])
        }
      );
    }
  } else if (enable_MarketRisk_Tab_Backward_Looking) {
    tooltipData.push({
      color: "#C7DA41",
      name: "گذشته‌نگر",
      y: findY(past)
    });
  }

  if (pastJensens !== undefined) {
    const pastJensensValue = toFixedNumber(pastJensens || 0, 4);
    tooltipData.push({
      color: "transparent",
      name: "مقدار آلفای جنسن",
      y: isEfficiency ? pastJensensValue * 100 : pastJensensValue
    });
  }

  if (enable_MarketRisk_Tab_Forward_Looking && !isRiskValue && data?.compareListCount) {
    tooltipData.push({
      needleColor: compareValueColor?.[0],
      name: `میانگین ${data?.compareListCount} صندوق `,
      y: findY(compareValue?.[0])
    });
  }

  const onClickHandle = () => onCardClick(id);

  return (
    <div
      onClick={onClickHandle}
      className={twMerge(
        "flex flex-col h-full rounded-lg p-[11px] pb-[15px] justify-between cursor-pointer bg-dark_black border border-cardBackground hover:border-borderBorderAndDivider",
        isSelected && "border-2 !border-accept_green08"
      )}
      data-test={`card-${id}`}
    >
      <div className="flex flex-col gap-2">
        <div className="flex justify-between">
          <h5>{title}</h5>
          <InfoCardTooltip id={id} data={tooltipData} isRiskValue={isRiskValue} />
        </div>
        <span className="text-xs leading-4">{desc}</span>
      </div>

      <div className="flex justify-center items-center">
        <CircleGauge
          needleValue={compareValue}
          needleColors={compareValueColor}
          size="medium"
          data={structor}
          arrowColors={["#26D5C0", "#C7DA41"]}
          labelFormatter={item =>
            // eslint-disable-next-line no-nested-ternary
            isEfficiency
              ? `${item?.min?.toFixed(2)?.replace(/\.00$/, "")}٪`
              : id === "riskValue"
                ? convertIntToCurrency(Number(item?.min) / 10, 0).value +
                  convertIntToCurrency(Number(item?.min) / 10).unit
                : item?.min?.toFixed(2)?.replace(/\.00$/, "")
          }
          value={
            // eslint-disable-next-line no-nested-ternary
            isEfficiency
              ? isShowRetrospect
                ? values?.map(item => item && item * 100)
                : [Number(values?.[0]) * 100, null]
              : values
          }
          lastLabel={
            structor?.length > 0
              ? // eslint-disable-next-line no-unsafe-optional-chaining
                structor
                  ?.sort((a, b) => Number(a?.max) - Number(b?.max))
                  ?.[Number(structor?.length) - 1]?.max?.toFixed(2)
                  ?.replace(/\.00$/, "") + (isEfficiency ? "٪" : "")
              : ""
          }
          insideCircleClassName="bg-dark_black"
        />
      </div>
    </div>
  );
}

export default memo(GaugeMarketCard);
