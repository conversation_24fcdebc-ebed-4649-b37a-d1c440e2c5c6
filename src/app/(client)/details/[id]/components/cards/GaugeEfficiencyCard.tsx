import CheckBox from "@/components/Atoms/checkBox/CheckBox";
import CircleGauge from "@/components/Atoms/circleGauge";
import { useGetFundMarketRisksListQuery } from "@/queries/fundsAPI";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { toFixedNumber } from "@/utils/helpers";
import { parseAsBoolean, useQueryStates } from "nuqs";
import { memo, useMemo } from "react";
import { twMerge } from "tailwind-merge";
import InfoCardTooltip from "./InfoCardTooltip";
import { EFFICIENCY_GAUGE, IGaugeEfficiencyCardProps } from "./type";
import { efficiencyColors } from "./util";

function GaugeEfficiencyCard(props: IGaugeEfficiencyCardProps) {
  const {
    id,
    title,
    desc,
    structor = [],
    past,
    future,
    real,
    isSelected,
    isShowRetrospect,
    onCardClick,
    compareValue,
    compareValueColor,
    fundId,
    isYtm
  } = props;

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { enable_MarketRisk_Tab_Forward_Looking_ActualReturn, enable_MarketRisk_Tab_Backward_Looking } =
    useModulesStore();

  const { data: fundQuery } = useGetFundMarketRisksListQuery(fundId);
  const { data } = fundQuery || {};

  const [queryStates, setQueryStates] = useQueryStates({
    isShowExpected: parseAsBoolean.withDefault(true),
    isShowReal: parseAsBoolean.withDefault(true)
  });

  const { isShowExpected, isShowReal } = queryStates;

  const checkboxList = [
    ...(enable_MarketRisk_Tab_Forward_Looking_ActualReturn // we dont want to show this checkbox if the module is not enabled because its not needed anymore
      ? [{ id: EFFICIENCY_GAUGE.EXPECTED, name: "مورد انتظار", varient: "filledGreen", isChecked: isShowExpected }]
      : []),
    ...(enable_MarketRisk_Tab_Forward_Looking_ActualReturn
      ? [{ id: EFFICIENCY_GAUGE.REAL, name: "واقعی", varient: "filledWhite", isChecked: isShowReal }]
      : [])
  ].filter(Boolean);

  const onCheck = (checkId: string) => {
    const list = [...checkboxList];
    const f = list.find(i => i.id.toString() === checkId);

    if (f) {
      setQueryStates({ [f.id]: !f.isChecked });
    }
  };

  const futureValue = toFixedNumber(future || 0, 3);
  const pastValue = toFixedNumber(past || 0, 3);
  const realValue = toFixedNumber(real || 0, 3);

  const findY = (item?: number) => {
    if (!item && item !== 0) return "-";
    return `${toFixedNumber((item || 0) * 100, 2)}٪`;
  };

  const { values, arrowColors, tooltipData } = useMemo(() => {
    const numbers = [];
    const colors = [];
    const info = [];

    if (isShowExpected) {
      numbers.push(futureValue || 0);
      colors.push(efficiencyColors.future);
      info.push({
        color: efficiencyColors.future,
        name: "آینده‌نگر",
        y: findY(future)
      });
    }

    if (isShowRetrospect && enable_MarketRisk_Tab_Backward_Looking) {
      numbers.push(pastValue || 0);
      colors.push(efficiencyColors.past);
      info.push({
        color: efficiencyColors.past,
        name: "گذشته‌نگر",
        y: findY(past)
      });
    }

    if (isShowReal && enable_MarketRisk_Tab_Forward_Looking_ActualReturn) {
      numbers.push(realValue || 0);
      colors.push(efficiencyColors.real);
      info.push({
        color: efficiencyColors.real,
        name: "واقعی",
        y: findY(real)
      });
    }

    if (compareValue) {
      info.push({
        needleColor: compareValueColor?.[0],
        name: `میانگین ${data?.compareListCount} صندوق`,
        y: findY(isYtm ? compareValue[0] / 100 : compareValue[0])
      });
    }

    return { values: numbers, arrowColors: colors, tooltipData: info };
  }, [futureValue, pastValue, realValue, isShowRetrospect, checkboxList]);

  const onClickHandle = () => onCardClick(id);
  const stopPropagation = (e: any) => e.stopPropagation();
  const onCheckHandle = (checkId: string) => () => onCheck(checkId);

  return (
    <div
      onClick={onClickHandle}
      className={twMerge(
        "flex flex-col h-full p-[11px] pr-14px rounded-lg justify-between cursor-pointer bg-dark_black border border-cardBackground hover:border-borderBorderAndDivider",
        isSelected && "border-2 !border-accept_green08"
      )}
      data-test={`card-${id}`}
    >
      <div className="flex flex-col gap-2">
        <div className="flex justify-between">
          <h5>{title}</h5>
          <InfoCardTooltip id={id} data={tooltipData} />
        </div>
        <span className="text-xs leading-4">{desc}</span>
      </div>

      <div className="flex justify-center items-start">
        <div className="flex flex-col gap-2 relative top-3 min-w-[59px]" onClick={stopPropagation}>
          {checkboxList.map(i => (
            <CheckBox
              checked={i.isChecked}
              onChange={onCheckHandle(i.id.toString())}
              text={i.name}
              size="small"
              variant={i.varient}
              textClassName="text-[10px] whitespace-nowrap"
            />
          ))}
        </div>
        <div className="flex grow justify-end">
          <CircleGauge
            size="medium"
            needleValue={compareValue}
            needleColors={compareValueColor}
            data={structor}
            arrowColors={arrowColors}
            labelFormatter={item => `${item?.min?.toFixed(2)?.replace(/\.00$/, "")}٪`}
            value={isShowExpected ? values?.map(item => item && item * 100) : [Number(values?.[0]) * 100, null]}
            lastLabel={
              structor?.length > 0
                ? `${structor
                    ?.sort((a, b) => Number(a?.max) - Number(b?.max))
                    ?.[Number(structor?.length) - 1]?.max?.toFixed(2)
                    ?.replace(/\.00$/, "")}٪`
                : ""
            }
            insideCircleClassName={isSelected ? "bg-dark_black" : "bg-backgroundCardBackground"}
          />
        </div>
      </div>
    </div>
  );
}

export default memo(GaugeEfficiencyCard);
