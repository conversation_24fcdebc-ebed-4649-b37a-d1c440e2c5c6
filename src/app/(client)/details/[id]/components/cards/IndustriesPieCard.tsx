import FundCardsSkeleton from "@/components/Atoms/skeleton/Skeleton";
import Pie<PERSON>hart from "@/components/Molecules/PieChart";
import EmptyPieChart from "@/components/Molecules/PieChart/EmptyPieChart";
import { useGetFundIndustriesSharePercentQuery } from "@/queries/fundsApiFront";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import { useRef } from "react";
import IndustriesModal from "../modals/industriesModal";

function IndustriesPieCard({ fundId, isEmpty }: { fundId: string; isEmpty: boolean }) {
  const { openUserModal } = useUserModalStore();

  const { data, isLoading } = useGetFundIndustriesSharePercentQuery(fundId);

  const chartRef = useRef<{ onClose: () => void }>(null);

  const newData = data?.data.map(item => ({
    name: item.name,
    y: item.sharePercent
  }));

  const onClickMaximize = () => {
    openUserModal(<IndustriesModal fundId={fundId} />, {
      headerTitle: "ترکیب صنایع",
      center: true,
      width: 432,
      titleClassName: "text-sm"
    });
  };

  return (
    <div className="h-full" data-test={`${fundId}-industries-pie-card`}>
      {isLoading ? (
        <div className="rounded-xl h-[120px] overflow-hidden">
          <FundCardsSkeleton />
        </div>
      ) : (
        <div
          className="flex justify-between items-center h-full pr-4 pl-3.5 cursor-pointer"
          onClick={() => {
            onClickMaximize();
            chartRef.current?.onClose();
          }}
        >
          <h5>صنایع</h5>

          {isEmpty || newData?.length === 0 ? (
            <EmptyPieChart />
          ) : (
            <div className="relative -left-2">
              <PieChart
                ref={chartRef}
                width={100}
                height={100}
                data={newData || []}
                id={`industries-${fundId}123`}
                isTooltipEnabled
                hasTooltipPercent
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default IndustriesPieCard;
