/* eslint-disable @typescript-eslint/naming-convention */
import ArrowBox from "@/assets/icons/box-arrow-top.svg";
import FundCardsSkeleton from "@/components/Atoms/skeleton/Skeleton";
import HighChart from "@/components/Molecules/highChart";
import { bindFundTypeToLabel } from "@/components/Organism/singleFundCard/utils";
import { useGetAssetValuesInfoCardByChart } from "@/queries/FundChartsAPI";
import { useGetOneFundBaseInfoQuery } from "@/queries/fundsApiFront";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { commaSeparator, roundUpToTwoDecimals } from "@/utils/helpers";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { parseAsBoolean, useQueryStates } from "nuqs";
import { useMemo } from "react";
import { getSeriesOfInfoCard } from "./util";

function CompanyInfoBox({ fundId }: { fundId: string }) {
  const pathname = usePathname();
  const { data, isLoading } = useGetOneFundBaseInfoQuery(fundId);

  // zustand hook
  const {
    enable_BondInstrument_List,
    enable_ShareInstrument_List,
    enable_Symbol: symbol,
    enable_FundType: fundType,
    enable_Fund_Value_Assets_Chart
  } = useModulesStore();

  const { data: raw } = useGetAssetValuesInfoCardByChart(fundId);
  const { data: dataAssetChart } = raw || {};
  const { graphPoints = [] } = dataAssetChart || {};

  const [queryStates] = useQueryStates({
    isShowMarketValue: parseAsBoolean.withDefault(true)
  });
  const { isShowMarketValue } = queryStates;

  const options = useMemo(
    () =>
      getSeriesOfInfoCard({
        graphPoints,
        field1: {
          id: "fundTotalValue",
          title: ""
        }
      }),
    [raw, isShowMarketValue]
  );

  return (
    <div className="h-full">
      {isLoading ? (
        <div className="rounded-xl h-[120px] overflow-hidden">
          <FundCardsSkeleton />
        </div>
      ) : (
        <div className="flex items-center justify-between pl-2 pr-3 py-2 rounded-xl bg-backgroundCardBackground">
          <div className="flex gap-3">
            <img alt="" src={`data:image/svg+xml;base64,  ${data?.data?.logoImage}`} className="w-20 h-[88px]" />

            <div className="flex flex-col gap-[7.5px]">
              <h1
                className="flex gap-2 text-base font-bold leading-[25px] text-borderLightGray"
                aria-label="asset-title"
              >
                {data?.data?.fullName}
              </h1>

              <div className="flex items-center gap-2 leading-6">
                {symbol && (
                  <span className="text-base leading-[25px] text-secondaryText" aria-label="asset-symbol-name">
                    {data?.data?.symbolName}
                  </span>
                )}
                {fundType && (
                  <span
                    className="px-3 leading-6 text-xs rounded-3xl text-gray1 bg-cardBackground"
                    aria-label="asset-type"
                  >
                    {data?.data?.fundType && bindFundTypeToLabel[data?.data?.fundType]}
                  </span>
                )}
              </div>

              {(enable_BondInstrument_List || enable_ShareInstrument_List) && (
                <Link
                  href={`${pathname}?modalType=symbolList`}
                  target="_blank"
                  data-test="2720bbfd-1f06-4fb0-b45c-7b2477154123"
                >
                  <div className="flex items-center gap-2 cursor-pointer text-accept_green08">
                    <ArrowBox width={16} height={16} className="" />
                    <span className="font-normal">لیست نمادها</span>
                  </div>
                </Link>
              )}
            </div>
          </div>

          {enable_Fund_Value_Assets_Chart && (
            <div className="flex flex-col justify-between items-end">
              <div className="flex flex-col pr-2 py-1 rounded-lg bg-dark_black7">
                <div className="flex gap-2 items-center">
                  {/* <span className="w-2 h-2 rounded-full bg-cyan1" /> */}
                  <h5 className="text-sm leading-6 text-secondaryText pl-2">
                    خالص ارزش دارایی :{" "}
                    <span aria-label="asset-total-value">
                      {commaSeparator(roundUpToTwoDecimals(Number(data?.data?.totalAssetValue) / 10000000000), 2)}
                    </span>{" "}
                    میلیارد تومان
                  </h5>
                </div>
                <div className="flex w-[252px] h-[64px] [&_.chart-wrapper]:!w-full [&_.highcharts-container]:!w-full">
                  <HighChart options={options} showTunedPeriod />
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default CompanyInfoBox;
