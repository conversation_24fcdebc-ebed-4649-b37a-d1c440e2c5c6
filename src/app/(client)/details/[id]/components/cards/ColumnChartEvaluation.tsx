/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from "react";
import { ColumnChart } from "@/components/Molecules/columnChart";
import { ORGANIZATION_STATUS_LABELS } from "@/components/Molecules/heatMap/util";
import { useGetFundColumnChartEvaluationQuery } from "@/queries/fundsAPI";
import { getColumnChartOption } from "@/components/Molecules/columnChart/util";
import { columnChartColorsDark } from "./util";

function ColumnChartEvaluation({ fundId, refresh }: { fundId: string; refresh: number }) {
  const { data: tooltipQuery, isLoading, refetch } = useGetFundColumnChartEvaluationQuery(fundId);

  useEffect(() => {
    if (refresh) {
      refetch();
    }
  }, [refresh]);

  const {
    criticalRiskPercent = 0,
    highRiskPercent = 0,
    lowRiskPercent = 0,
    mediumRiskPercent = 0
  } = tooltipQuery?.data || {};

  const categories = [
    ORGANIZATION_STATUS_LABELS.LOW,
    ORGANIZATION_STATUS_LABELS.MODERATE,
    ORGANIZATION_STATUS_LABELS.HIGH,
    ORGANIZATION_STATUS_LABELS.CRITICAL
  ];

  const series = [
    {
      data: [
        { y: Math.round(lowRiskPercent), color: columnChartColorsDark[0] },
        { y: Math.round(mediumRiskPercent), color: columnChartColorsDark[1] },
        { y: Math.round(highRiskPercent), color: columnChartColorsDark[2] },
        { y: Math.round(criticalRiskPercent), color: columnChartColorsDark[3] }
      ]
    }
  ];

  const options = getColumnChartOption({ series, categories, tooltipEnabled: false, pointWidth: 32 });

  return (
    <div className="flex flex-col grow h-full rounded-lg pt-2 bg-backgroundCardBackground">
      <h4 className="text-sm leading-8 font-bold mb-3 pl-2 pr-3 text-borderLightGray">ریسک ها براساس شدت</h4>
      {!isLoading && <ColumnChart options={options} className="grow p-[2px] pl-[15px]" />}
    </div>
  );
}

export default ColumnChartEvaluation;
