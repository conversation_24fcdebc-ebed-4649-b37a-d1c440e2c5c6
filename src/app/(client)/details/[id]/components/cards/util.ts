/* eslint-disable no-plusplus */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */

import getLineChartOptions, { defaultColors, renderRecord } from "@/app/(client)/details/[id]/components/charts/util";
import { toBillion } from "@/components/Molecules/barChart/util";
import { FUND, TFundRiskRow, TFundRow } from "@/queries/fundsAPI/types";
import { FOCUS_SUB_TABS, FUND_SUB_TABS, FUND_TABS, TSummaryMarketRiskOptions } from "./type";

export const isNullOrEmpty = (v?: number | null) => v == null || v === undefined;

export const convertFundData = (flatData: TFundRiskRow[]) => {
  const data: TFundRow[] = [];

  flatData.forEach(row => {
    for (const key in FUND) {
      const { title, desc = "", enum1 } = FUND[key];
      if (enum1.includes(row.fundRiskIndex)) {
        const f: number = data.findIndex(c => c.id === key);
        if (f === -1) {
          data.push({ id: key, title, desc, past: row, future: null });
        } else {
          data[f].future = row;
        }
      }
    }
  });

  return data;
};

export const getSummaryMarketRisksOptions = (future: number, past: number, checked?: boolean) => {
  const list = [];

  if (future !== -1) {
    list.push({ color: "#26D5C0", value: future });
  }

  if (checked) {
    list.push({ color: "#C7DA41", value: past });
  }
  return list;
};

export const getSummaryMarketEfficiencyOptions = (params: TSummaryMarketRiskOptions) => {
  const { future, past, real, isShowRetrospect, isShowExpected, isShowReal } = params;

  const list = [];

  if (isShowExpected) {
    list.push({ color: "#26D5C0", value: future });
  }

  if (isShowRetrospect) {
    list.push({ color: "#C7DA41", value: past });
  }

  if (isShowReal) {
    list.push({ color: "#F4F4F4", value: real });
  }

  return list;
};

export const getSummaryInterestOptions = (value1: number, value2?: number, colors?: string[]) => {
  const list = [{ color: colors && colors[0] ? colors[0] : "#55C3FF", value: value1 }];
  if (value2) {
    list.push({ color: colors && colors[1] ? colors[1] : "#43E5A0", value: value2 });
  }
  return list;
};

export const getSummaryInterestOptions2 = (values: number[], colors: string[]) => {
  const list: any[] = [];

  values.forEach((value, index) => {
    list.push({ value, color: colors[index] });
  });

  return list;
};

export const getSeriesOfMarketRiskFormat = (props: {
  graphPoints: any[];
  checked: boolean;
  futureField: string;
  pastField: string;
  hasPercent?: boolean;
  colorsArg?: string[];
}) => {
  const { graphPoints, checked, futureField, pastField, hasPercent, colorsArg } = props;
  const count = graphPoints?.length || 0;
  let colors = colorsArg || defaultColors;

  if (futureField === "") {
    colors = colors.slice(1);
  }

  const futureData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[futureField], index, count, color: colors?.[0] || null })
  );

  const pastData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[pastField], index, count, color: colors?.[1] || null })
  );

  const series = [];

  if (futureField !== "") {
    series.push({ name: "آینده نگر", data: futureData || [], type: "area", fillColor: "transparent" });
  }

  series.push({ name: "گذشته نگر", data: pastData || [], visible: checked, type: "area", fillColor: "transparent" });

  return getLineChartOptions({ data: series, hasPercent, chartColors: colors });
};

export const getSeriesOfEfficiencyMarketRiskFormat = (props: {
  graphPoints: any[];
  isShowRetrospect: boolean;
  isShowExpected: boolean;
  isShowReal: boolean;
  futureField: string;
  pastField: string;
  realField?: string;
  hasPercent?: boolean;
  colorsArg?: string[];
}) => {
  const {
    graphPoints,
    isShowRetrospect,
    isShowExpected,
    isShowReal,
    futureField,
    pastField,
    realField,
    hasPercent,
    colorsArg
  } = props;

  const count = graphPoints?.length || 0;
  const colors = colorsArg || defaultColors;

  const futureData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[futureField], index, count, color: colors?.[0] || null })
  );

  const pastData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[pastField], index, count, color: colors?.[1] || null })
  );

  const realData = realField
    ? graphPoints?.map((i, index) =>
        renderRecord({ dateTime: i.checkpointDate, value: i[realField], index, count, color: colors?.[2] || null })
      )
    : [];

  const series = [];

  if (futureField !== "") {
    series.push({
      name: "آینده نگر",
      data: futureData || [],
      type: "area",
      fillColor: "transparent",
      visible: isShowExpected
    });
  }

  series.push({
    name: "گذشته نگر",
    data: pastData || [],
    visible: isShowRetrospect,
    type: "area",
    fillColor: "transparent"
  });

  if (realData) {
    series.push({ name: "واقعی", visible: isShowReal, data: realData || [], type: "area", fillColor: "transparent" });
  }

  return getLineChartOptions({ data: series, hasPercent, chartColors: colors });
};

export const getSeriesOfInterestRiskFormat = (props: {
  graphPoints: any[];
  field1: { id: string; title: string };
  field2?: { id: string; title: string };
  moreFields?: { id: string; title: string }[];
  colors?: string[];
  checked?: boolean;
  hasPercent?: boolean;
}) => {
  const { graphPoints, field1, field2, moreFields, colors, hasPercent, checked = true } = props;
  const graphPointsData1 = graphPoints?.filter(item => !isNullOrEmpty(item?.[field1?.id]));
  const chartColors = colors || ["#55C3FF", "#43E5A0"];
  let colorIndex = 0;

  const data1 = graphPointsData1?.map((i, index) =>
    renderRecord({
      dateTime: i.checkpointDate,
      value: i[field1.id],
      index,
      count: graphPointsData1?.length || 0,
      color: chartColors?.[colorIndex] || null
    })
  );

  const series = [{ name: field1.title, data: data1 || [], type: "area", visible: true, fillColor: "transparent" }];

  if (field2) {
    const graphPointsData2 = graphPoints?.filter(item => !isNullOrEmpty(item?.[field2?.id]));
    colorIndex++;

    const data2 = graphPointsData2?.map((i, index) =>
      renderRecord({
        dateTime: i.checkpointDate,
        value: i[field2?.id],
        index,
        count: graphPointsData2?.length || 0,
        color: chartColors?.[colorIndex] || null
      })
    );

    series.push({
      name: field2.title,
      data: data2 || [],
      type: "area",
      visible: checked,

      fillColor: "transparent"
    });
  }

  if (moreFields) {
    moreFields?.forEach(f => {
      const graphPointsMoreData = graphPoints?.filter(item => !isNullOrEmpty(item?.[f?.id]));
      colorIndex++;

      const data = graphPointsMoreData?.map((i, index) =>
        renderRecord({
          dateTime: i.checkpointDate,
          value: i[f.id],
          index,
          count: graphPointsMoreData?.length || 0,
          color: chartColors?.[colorIndex] || null
        })
      );

      series.push({ name: f.title, data: data || [], type: "area", visible: true, fillColor: "transparent" });
    });
  }

  return getLineChartOptions({ data: series, chartColors, hasPercent });
};

export const getSeriesOfInfoCard = (props: { graphPoints: any[]; field1: { id: string; title: string } }) => {
  const { graphPoints, field1 } = props;
  const graphPointsData = graphPoints?.filter(item => !isNullOrEmpty(item?.[field1?.id]));
  const count = graphPointsData?.length || 0;
  const chartColors = ["#2A9AB0"];

  // map start
  const data1 = graphPointsData?.map((i, index) => {
    const list = renderRecord({
      dateTime: i.checkpointDate,
      value: i[field1.id],
      index,
      count,
      color: chartColors?.[0] || null
    });

    if (index === count - 1) {
      return { ...list, marker: { enabled: true } };
    }

    return list;
  });
  // map end

  const series = [
    {
      name: field1.title,
      data: data1 || [],
      type: "area",
      visible: true,
      shadow: { enabled: true, color: "rgba(38,213,192,0.6)" },
      fillColor: "transparent"
    }
  ];

  const options = getLineChartOptions({ data: series, chartColors, hasPercent: false });
  const values = data1.map(i => i.y) || [];
  const max = Math.max.apply(null, values);
  const tickInterval = Math.floor(max / 4);

  return {
    ...options,
    chart: {
      ...options.chart,
      marginTop: 10,
      marginBottom: 10,
      marginRight: 12,
      // height: `60%`,
      zooming: { enabled: false }
    },
    xAxis: { visible: false },
    yAxis: {
      ...options.yAxis,
      startOnTick: false,
      gridLineColor: "#545454",
      tickInterval,
      labels: {
        useHTML: true,
        /* @ts-ignore */
        // eslint-disable-next-line
        formatter: function () {
          /* @ts-ignore */
          return this.value ? `${Math.trunc(toBillion(this.value))}B` : "0";
        },
        style: { color: "#F4F4F4", fontFamily: "var(--font-yekan)", fontSize: "10px" }
      }
    },
    tooltip: { enabled: false },
    plotOptions: { ...options.plotOptions, series: { ...options.plotOptions?.series, enableMouseTracking: false } }
  };
};

export const tabs = [
  { id: FUND_TABS.MARKET_RISK, title: "ریسک بازار" },
  { id: FUND_TABS.INTEREST_RISK, title: "ریسک نرخ بهره" },
  { id: FUND_TABS.ADAPTIVE_RISK, title: "ریسک تطبیق" },
  { id: FUND_TABS.OPERATIONAL_RISK, title: "ریسک عملیاتی" },
  { id: FUND_TABS.FUND_ASSETS_LIQUID, title: "ریسک نقدشوندگی" },
  { id: FUND_TABS.LIQUIDITY_RISK, title: "ریسک نقدینگی" },
  { id: FUND_TABS.FOCUS_RISK, title: "ریسک تمرکز" }
];

export const operationRisk_subTabs = [
  { id: FUND_SUB_TABS.EVALUATION, title: "ارزیابی ریسک ها" },
  { id: FUND_SUB_TABS.RISK_MANAGEMENT, title: "استراتژی مدیریت ریسک" }
];

export const focusRisk_subTabs = [
  { id: FOCUS_SUB_TABS.SHAREHOLDERS, title: "سهامدارها" },
  { id: FOCUS_SUB_TABS.ASSETS, title: "دارایی‌ها" }
];

export const getColorByValue = (n?: number) => {
  let color = "text-[#A5F539]";
  if (n === undefined) {
    color = "text-[#676767]";
  } else if (n <= 40) {
    color = "text-[#FF5E5E]";
  } else if (n > 40 && n <= 70) {
    color = "text-[#F1C21B]";
  }

  return color;
};

export const getBorderColorByValue = (n: number) => {
  let color = "border-[#A5F539]";
  if (n <= 40) {
    color = "border-[#FF5E5E]";
  } else if (n > 40 && n <= 70) {
    color = "border-[#F1C21B]";
  }

  return color;
};

export const efficiencyColors = { future: "#26D5C0", past: "#C7DA41", real: "#F4F4F4" };

export const columnChartColorsLight = ["#108554", "#FAEB8E", "#E1AB11", "#E35050"];
export const columnChartColorsDark = ["#0d6a43", "#c8bc72", "#b4890e", "#b64040"];
