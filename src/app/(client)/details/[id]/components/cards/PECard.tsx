import CloseIcon from "@/assets/icons/close-user-modal.svg";
import CircleGauge from "@/components/Atoms/circleGauge";
import { IZone } from "@/components/Atoms/circleGauge/types";
import FundCardsSkeleton from "@/components/Atoms/skeleton/Skeleton";
import { useGetPEDataQuery } from "@/queries/fundsAPI";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import { twMerge } from "tailwind-merge";

import { useGetAllFundsFrontQuery } from "@/queries/fundsApiFront";
import PEModal from "./PEModal";

function PECard({ fundId }: { fundId: string }) {
  const { openUserModal, closeUserModal } = useUserModalStore();
  const { data, isLoading } = useGetPEDataQuery(fundId);
  // const { openUserModal } = useUserModalStore();

  const bondsPriceToEarnings =
    data?.data?.bondsPriceToEarnings?.orderedRanges || data?.data?.investmentGroupPriceToEarnings?.orderedRanges || [];

  const peData = bondsPriceToEarnings;
  const { data: allFunds } = useGetAllFundsFrontQuery();
  const fundViewConfig = allFunds?.data?.find(item => item?.id === fundId)?.fundViewConfig;

  const sortedData = peData
    // eslint-disable-next-line no-unsafe-optional-chaining
    ?.sort((a, b) => a?.minimum || 0 - b?.minimum || 0)
    ?.map((item, index) => ({
      id: index,
      type: item?.levelType,
      level: item?.level,
      min: item?.minimum,
      max: item?.maximum
    })) as unknown as IZone[];

  const peValues = [
    fundViewConfig?.enable_PricePerEPSForBondPortfolio ? (data?.data?.bondsPriceToEarnings?.value ?? null) : null,
    data?.data?.investmentGroupPriceToEarnings?.value ?? null,
    fundViewConfig?.enable_PricePerEPSForStockPortfolio ? (data?.data?.sharesPriceToEarnings?.value ?? null) : null
  ]?.filter(Boolean);

  const handleOpenPEModal = () => {
    openUserModal(<PEModal fundId={fundId} />, {
      width: "fit-content",
      center: true,
      headerTitle: <span className="text-sm font-bold">جزئیات P/E</span>,

      closeIcon: (
        <CloseIcon onClick={closeUserModal} width={24} height={24} className="cursor-pointer text-borderLightGray" />
      )
    });
  };

  return (
    <div className="h-full" data-test={`${fundId}-pe-card`}>
      {isLoading ? (
        <div className="rounded-xl h-[112px] overflow-hidden">
          <FundCardsSkeleton />
        </div>
      ) : (
        <div
          className={twMerge(
            "flex justify-between items-center bg-backgroundCardBackground h-full rounded-xl px-4 py-3 cursor-pointer"
          )}
          onClick={handleOpenPEModal}
        >
          <h5 className="text-16 font-bold">P/E </h5>

          <CircleGauge
            size="small"
            ringBorderPercentage={1.2}
            data={sortedData}
            value={peValues}
            stripSize={8}
            arrowColors={
              [
                fundViewConfig?.enable_PricePerEPSForBondPortfolio ? "#B5179E" : null,
                "#26ECA0",
                fundViewConfig?.enable_PricePerEPSForStockPortfolio ? "#0C82F9" : null
              ].filter(Boolean) as any
            }
            insideCircleClassName="bg-backgroundCardBackground"
            labelFormatter={item => item?.min?.toFixed(2)?.replace(/\.00$/, "")}
            lastLabel={
              sortedData?.length > 0
                ? sortedData
                    ?.sort((a, b) => Number(a?.max) - Number(b?.max))
                    ?.[Number(sortedData?.length) - 1]?.max?.toFixed(2)
                    ?.replace(/\.00$/, "")
                : ""
            }
          />
        </div>
      )}
    </div>
  );
}

export default PECard;
