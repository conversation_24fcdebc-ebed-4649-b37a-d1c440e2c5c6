import FundCardsSkeleton from "@/components/Atoms/skeleton/Skeleton";
import <PERSON><PERSON>hart from "@/components/Molecules/PieChart";
import EmptyPieChart from "@/components/Molecules/PieChart/EmptyPieChart";
import { bindCompositionTypeToColor } from "@/components/Organism/singleFundCard/utils";
import { useGetFundAssetsCompositionQuery } from "@/queries/fundsApiFront";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import { useRef } from "react";
import { twMerge } from "tailwind-merge";
import AssetModal from "../modals/assetModal";

function AssetPieCard({ fundId, isEmpty }: { fundId: string; isEmpty: boolean }) {
  const { openUserModal } = useUserModalStore();
  const { data, isLoading } = useGetFundAssetsCompositionQuery(fundId);

  const chartRef = useRef<{ onClose: () => void }>(null);

  const pieChartData = data?.data?.map(item => ({
    name: item.title,
    y: item.assetCompositionPercent,
    color: bindCompositionTypeToColor[item.assetCompositionType]
  }));

  const onClickMaximize = () => {
    openUserModal(<AssetModal fundId={fundId} />, {
      headerTitle: "ترکیب دارایی",
      center: true,
      width: 432,
      titleClassName: "text-sm"
    });
  };

  return (
    <div className="h-full" data-test={`${fundId}-asset-pie-card`}>
      {isLoading ? (
        <div className="rounded-xl h-[120px] overflow-hidden">
          <FundCardsSkeleton />
        </div>
      ) : (
        <div
          className={twMerge("flex justify-between items-center h-full pr-[18px] cursor-pointer")}
          onClick={() => {
            onClickMaximize();
            chartRef.current?.onClose();
          }}
        >
          <h5>دارایی</h5>

          {isEmpty || pieChartData?.length === 0 ? (
            <EmptyPieChart />
          ) : (
            <div className="relative -left-2">
              <PieChart
                ref={chartRef}
                width={100}
                height={100}
                data={pieChartData || []}
                id={`fund-${fundId}`}
                isTooltipEnabled
                hasTooltipPercent
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default AssetPieCard;
