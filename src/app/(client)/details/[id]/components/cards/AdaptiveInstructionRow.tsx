import { twMerge } from "tailwind-merge";
import { getBorderColorByValue, getColorByValue } from "./util";

const AdaptiveInstructionRow = (props: { title: string; value?: number; isFirst: boolean }) => {
  const { title, value, isFirst } = props;
  const textColor = getColorByValue(value || 0);
  const borderColor = getBorderColorByValue(value || 0);

  return (
    <div
      className={twMerge(
        "flex justify-between items-center px-2 text-base rounded opacity-80 border-r-2 border-r-transparent h-8 bg-backgroundBodyBackground",
        isFirst && value !== undefined && borderColor
      )}
    >
      <span className="text-xs leading-4">{title}</span>
      <div className="leading-[25px] ltr">
        <span className={twMerge(isFirst && value !== undefined && textColor)}>
          {value !== undefined ? Math.round(value) : "-"}
          {isFirst && " %"}
        </span>
      </div>
    </div>
  );
};

export default AdaptiveInstructionRow;
