import CheckBox from "@/components/Atoms/checkBox/CheckBox";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { twMerge } from "tailwind-merge";
import { IFundChartFilter } from "./type";

function FundChartFilter(props: IFund<PERSON>hartFilter) {
  const { isCheckedRetrospect, className, onChange } = props;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { enable_MarketRisk_Tab_Backward_Looking } = useModulesStore();

  return (
    <div className={twMerge("flex items-center gap-3.5 text-10", className)}>
      <div className="flex gap-1">
        <span className="w-3 h-3 rounded-sm bg-withCouponCyan" />
        آینده‌ نگر
      </div>

      {enable_MarketRisk_Tab_Backward_Looking && (
        <CheckBox
          checked={isCheckedRetrospect}
          onChange={onChange}
          text="گذشته‌نگر"
          size="small"
          variant="filledYellow"
          textClassName="!text-10 font-normal"
          id="retrospect-checkbox"
        />
      )}
    </div>
  );
}

export default FundChartFilter;
