import { dateConverter } from "@/utils/DateHelper";

const AdaptiveInstructionHeading = ({ title, date }: { title: string; date?: string }) => {
  const persianDate = dateConverter(date || "").format("YYYY/MM/DD");

  return (
    <div className="flex justify-between text-xs font-normal leading-4 mb-1">
      <h5>{title}</h5>
      {date && <span>{persianDate}</span>}
    </div>
  );
};

export default AdaptiveInstructionHeading;
