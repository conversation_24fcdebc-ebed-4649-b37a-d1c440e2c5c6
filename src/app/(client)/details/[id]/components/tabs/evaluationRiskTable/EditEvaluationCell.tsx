/* eslint-disable no-nested-ternary */
// CellComponent.js

import React, { useLayoutEffect, useMemo, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";
import Save from "@/assets/icons/editable-table-save.svg";
import Edit from "@/assets/icons/editable-table-edit.svg";
import Close from "@/assets/icons/editable-table-close.svg";
import Arrow from "@/assets/icons/editable-table-arrow-down.svg";
import { AdaptiveRiskData } from "@/queries/adaptiveRiskAPI/types";
import FilterDropdown from "@/components/Molecules/filterDropDown/FilterDropDown";
import useEditOperationWithStrategyTableStore from "@/app/(client)/details/[id]/components/tabs/evaluationRiskTable/userTableStore";
import { usePostManipulateRiskMutation } from "@/queries/fundsAPI";
import { Spinner } from "@/components/Atoms/spinner";

export const impactRateTranslate = {
  5: "بسیار شدید",
  4: "نسبتا شدید",
  3: "متوسط",
  2: "جزئی",
  1: "ناچیز"
};

export const probabilityTranslate = {
  1: "نادر",
  2: "غیرمحتمل",
  3: "امکان پذیر",
  4: "احتمال دارد",
  5: "تقریبا مطمئن"
};

export const organizationalUnit = {
  100: "امور سهام",
  200: "امور شرکت ها و مجامع",
  300: "بازارگردانی",
  400: "برنامه ریزی و راهبرد",
  500: "حقوقی",
  600: "سرمایه گذاری",
  700: "فناوری اطلاعات و تحقیق و توسعه",
  800: "مالی و اداری"
};

export const organizationalUnitItems = Object.entries(organizationalUnit).map(([id, label]) => ({
  label,
  id,
  name: id
}));
export const impactRateItems = Object.entries(impactRateTranslate).map(([id, label]) => ({
  label,
  id,
  name: id
}));

export const probabilityTranslateItems = Object.entries(probabilityTranslate).map(([id, label]) => ({
  label,
  id,
  name: id
}));

interface IEditCellProps {
  rowIndex: number;
  data: AdaptiveRiskData;
  updateChart: () => void;
  isCompact?: boolean;
  isModal?: boolean;
}
const EditEvaluationCell = ({ data, rowIndex, updateChart, isCompact, isModal }: IEditCellProps) => {
  const [rowId, setRowId] = useState("");
  const [impactRate, setImpactRate] = useState<number>(data.impactRate);
  const [probability, setProbability] = useState<number>(data.probability);
  const divRef = useRef<HTMLDivElement | null>(null);
  const { editRowId, setEditRowId } = useEditOperationWithStrategyTableStore();
  const isSelectedRow = rowId === data?.id;
  const isEditing = editRowId === data?.id;
  const [disatnce, setDistance] = useState<string | undefined>(undefined);
  const { mutate: manipulateRisks, isLoading: isManipulateRisksLoading } = usePostManipulateRiskMutation();
  const [isLoading, setIsLoading] = useState(false);

  const item = useMemo(() => data, [data]);

  const handleMouseEnter = () => {
    if (!isEditing) setRowId(data.id);
  };

  const handleMouseLeave = () => {
    if (!isEditing) setRowId("");
  };

  const handleEditClick = () => {
    setEditRowId(data?.id);
    setRowId("");
  };

  const handleCloseClick = () => {
    setEditRowId(undefined);
  };

  const handleSaveClick = () => {
    setEditRowId(undefined);
    setIsLoading(true);
    manipulateRisks(
      {
        id: data?.id,
        impactRate,
        probability
      },
      {
        onSuccess: () => {
          updateChart();
          // Scroll to the element after successful save
          const elementId = `${item?.id}-${rowIndex}`;
          const element = document.getElementById(elementId);
          if (element) {
            element.scrollIntoView({
              behavior: "smooth",
              block: "center"
            });
          }
        }
      }
    );

    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  };

  const updateWidth = () => {
    if (divRef.current) {
      const parentWidth = divRef.current.parentElement?.getBoundingClientRect().width || 0;
      setDistance(`${parentWidth + (isModal ? 320 : isCompact ? 140 : 180)}px`);
    }
  };

  useLayoutEffect(() => {
    const resizeObserver = new ResizeObserver(updateWidth);
    if (divRef.current?.parentElement) {
      resizeObserver.observe(divRef.current.parentElement);
    }

    return () => resizeObserver.disconnect();
  }, []);

  return (
    <div
      ref={divRef}
      style={{ width: disatnce }}
      id={`${item?.id}-${rowIndex}`}
      // style={{ width: disatnce ? `${disatnce}px` : "" }}
      className={twMerge(
        isSelectedRow || isEditing ? "bg-[#343438] rounded border-r border-r-[#545454]" : "",
        "h-full fixed z-1000 !left-0 border-r border-r-transparent cursor-pointer"
      )}
    >
      <div className="pt-4" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
        <div
          className={twMerge(
            "flex justify-between text-white relative",
            isModal ? "pl-[90px] pr-[50px]" : isCompact ? "pr-[40px] pl-10" : "pr-[70px] pl-16"
          )}
        >
          {isSelectedRow && !isEditing && (
            <div className="absolute right-1" onClick={handleEditClick}>
              <Edit className="text-[#BDBDBD]" />
            </div>
          )}
          {isEditing && (
            <div className="absolute right-0 top-0 flex items-center gap-3">
              <div onClick={handleCloseClick}>
                <Close className="text-borderLightGray" />
              </div>
              <div onClick={handleSaveClick}>
                <Save className="text-[#BDBDBD]" />
              </div>
            </div>
          )}
          {isLoading || isManipulateRisksLoading ? (
            <Spinner className="justify-start" />
          ) : isEditing ? (
            <div className={isModal ? "pl-0" : "pl-16"}>
              <FilterDropdown
                // eslint-disable-next-line no-console
                onSelect={v => setImpactRate(Number(v?.id))}
                options={impactRateItems}
                RenderArrow={<Arrow />}
                hasAll={false}
                // defaultTitle="انتخاب ...      "
                defaultTitle={impactRateTranslate?.[item?.impactRate as keyof typeof impactRateTranslate]}
                showValueOnTitle
              />
            </div>
          ) : (
            <div className="text-sm font-normal text-textSecondary min-w-16 text-right">
              {impactRateTranslate?.[item?.impactRate as keyof typeof impactRateTranslate]}{" "}
            </div>
          )}
          {isLoading || isManipulateRisksLoading ? (
            <Spinner className="justify-center" />
          ) : isEditing ? (
            <div className={twMerge("pr-3 justify-center", isModal && "ml-[100px]")}>
              <FilterDropdown
                // eslint-disable-next-line no-console
                onSelect={v => setProbability(Number(v?.id))}
                options={probabilityTranslateItems}
                RenderArrow={<Arrow />}
                hasAll={false}
                // defaultTitle="انتخاب ...      "
                defaultTitle={probabilityTranslate?.[item?.probability as keyof typeof probabilityTranslate]}
                showValueOnTitle
              />
            </div>
          ) : (
            <div
              className={twMerge(
                "text-sm font-normal text-textSecondary min-w-16 text-center justify-center ",
                isModal && "ml-[100px]"
              )}
            >
              {probabilityTranslate?.[item?.probability as keyof typeof probabilityTranslate]}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EditEvaluationCell;
