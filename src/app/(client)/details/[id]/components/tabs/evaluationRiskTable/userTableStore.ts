import { create } from "zustand";

interface IEditOperationWithStrategyTableStore {
  editRowId?: string;
  setEditRowId: (content?: string) => void;
  scrollToElementId?: string | null;
  setScrollToElementId: (elementId?: string | null) => void;
}

const useEditOperationWithStrategyTableStore = create<IEditOperationWithStrategyTableStore>(set => ({
  editRowId: undefined,
  setEditRowId: content => set(() => ({ editRowId: content })),
  scrollToElementId: null,
  setScrollToElementId: elementId => set(() => ({ scrollToElementId: elementId }))
}));

export default useEditOperationWithStrategyTableStore;
