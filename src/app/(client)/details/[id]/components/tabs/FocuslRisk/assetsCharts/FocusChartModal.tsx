import CloseIcon from "@/assets/icons/Cancel.svg";
import MinimizeIcon from "@/assets/icons/minimize.svg";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import RenderFocusRiskChart from "./RenderFocusRiskChart";

function FocusChartAssetModal(props: { fundId: string; type: string; activeChip: string; onCloseModal: () => void }) {
  const { fundId, type, activeChip, onCloseModal } = props;

  const title = `نمودار تغییرات ${type}`;

  const { closeUserModal } = useUserModalStore();

  return (
    <>
      <div className="flex justify-between">
        <div className="flex gap-4 pb-3">{title}</div>
        <CloseIcon
          onClick={onCloseModal}
          width={24}
          height={24}
          className="cursor-pointer text-borderLightGray"
          aria-label="close modal"
        />
      </div>

      {/* Render line chart based on active tab */}
      <div className="relative flex grow">
        <RenderFocusRiskChart type={type} fundId={fundId} activeChips={activeChip} />
        <MinimizeIcon
          onClick={closeUserModal}
          className="absolute w-6 h-6 left-3 bottom-3 cursor-pointer"
          aria-label="minimize chart"
        />
      </div>
    </>
  );
}

export default FocusChartAssetModal;
