/* eslint-disable react/no-array-index-key */
import CheckBox from "@/components/Atoms/checkBox/CheckBox";
import { bindBgVariants } from "@/components/Atoms/checkBox/utils";
import DateFilter from "@/components/Organism/DateFilter";
import { isString, toFixed } from "@/utils/helpers";
import { memo } from "react";
import { twMerge } from "tailwind-merge";
import { IOwnerChartProps } from "./types";

function ChartFilterBox(props: IOwnerChartProps) {
  const { onFilterChange, filtersList, hasPercent } = props;

  const toNumber = (n: any) => {
    if (!isString(n)) {
      return hasPercent ? `٪ ${toFixed(n * 100)}` : toFixed(n);
    }

    return n;
  };

  return (
    <div className="flex justify-between p-3 pb-0 gap-2">
      <div className="flex items-center text-sm gap-3 leading-4 p-2 pt-1.5 rounded-lg right-3 top-1 z-10 bg-dark_black9 text-white">
        <div className="flex gap-3">
          {filtersList?.map(i => (
            <div key={i.id}>
              <span key={i.id} className="flex gap-1 items-center">
                {i.checkable ? (
                  <CheckBox
                    checked={i.checked}
                    onChange={() => onFilterChange?.(i.id)}
                    text={i?.title}
                    size="small"
                    variant={i.color as keyof typeof bindBgVariants}
                    textClassName="!text-xs font-normal"
                  />
                ) : (
                  <>
                    <i className={twMerge(`flex w-4 h-4 rounded-sm`)} style={{ background: i.color }} />
                    <span className="leading-4 text-xs ltr">{toNumber(i.title)}</span>
                  </>
                )}
              </span>
            </div>
          ))}
        </div>
      </div>
      <div className="z-50">
        <DateFilter />
      </div>
    </div>
  );
}

export default memo(ChartFilterBox);
