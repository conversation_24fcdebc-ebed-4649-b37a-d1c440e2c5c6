/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-array-index-key */
import { useState } from "react";
import { twMerge } from "tailwind-merge";
import HighChart from "@/components/Molecules/highChart";
import InfoIcon from "@/assets/icons/info.svg";
import InfoGreenIcon from "@/assets/icons/info-green.svg";
import { FUND_FOCUS } from "@/queries/fundsAPI/types";
import { commaSeparator, toFixed } from "@/utils/helpers";
import Loading from "@/components/Atoms/loading";
import { IHoldersCardsProps } from "./types";

export default function HoldersCards(props: IHoldersCardsProps) {
  const { crData, hhiData, activeCard, onCardClick, crChartLoading, hhiChartLoading } = props;

  const [isShow, setShow] = useState(false);

  const onMouseOver = () => setShow(true);
  const onMouseLeave = () => setShow(false);

  const onCardClickHandle = (id: string) => () => onCardClick(id);

  return (
    <div className="grid grid-cols-2 gap-2 h-[144px] ">
      {/* HHI Card */}

      {hhiData?.map(i => (
        <div
          onClick={onCardClickHandle(i.id)}
          className={twMerge(
            "flex flex-col p-2  h-full rounded-lg bg-dark_black hover:border-borderBorderAndDivider  cursor-pointer",
            i.id === activeCard && " outline outline-2 outline-accept_green08"
          )}
        >
          {hhiChartLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loading />
            </div>
          ) : (
            <div className="h-full flex  flex-col">
              <span className="flex gap-2 justify-between leading-6">
                <span className="text-base text-gray1">{i.title}</span>
                <span className="text-sm font-bold text-secondaryText">
                  {i.value !== null ? commaSeparator(toFixed(Number(i.value)), 2) : "-"}
                </span>
              </span>
              <div className="flex grow w-full [&_.chart-wrapper]:!w-full [&_.highcharts-container]:!w-full">
                <HighChart options={i.chartOption} />
              </div>
            </div>
          )}
        </div>
      ))}
      {/* End HHI */}

      {/* Three CR Cards */}

      <div
        onClick={onCardClickHandle(FUND_FOCUS.CR)}
        className={twMerge(
          "h-full flex  flex-col p-2  rounded-lg cursor-pointer bg-dark_black hover:border-borderBorderAndDivider",
          activeCard === FUND_FOCUS.CR && "outline outline-2 outline-accept_green08"
        )}
      >
        {crChartLoading ? (
          <div className="flex items-center justify-center h-full">
            <Loading />
          </div>
        ) : (
          <div className="h-full flex  flex-col">
            <span className="flex justify-between mb-2 ">
              <span className="text-base">CR</span>
              {!isShow ? (
                <InfoIcon onMouseOver={onMouseOver} width={16} height={16} className="text-white cursor-default" />
              ) : (
                <InfoGreenIcon onMouseLeave={onMouseLeave} width={16} height={16} className="cursor-default" />
              )}
            </span>

            <div className="cards flex gap-2 grow">
              {crData.map(i => (
                <div key={i.id} className="flex flex-col rounded w-full">
                  <span className="flex justify-between text-xs px-2 py-1 leading-4 bg-backgroundCardBackground rounded-t text-gray1">
                    <span>{i.title}</span>
                    <span>{i.value ? `%${toFixed(i.value)}` : "-"}</span>
                  </span>
                  <div className="flex grow w-full rounded-b bg-bodyBackground [&_.chart-wrapper]:!w-full [&_.highcharts-container]:!w-full">
                    <div className={twMerge("flex grow mb-2", isShow && "hidden")}>
                      <HighChart options={i.chartOption} />
                    </div>
                    {isShow && (
                      <div className="flex flex-col w-full">
                        {i.info?.map((j, k) => (
                          <div key={k} className="flex justify-between text-sm px-2 leading-6 items-center">
                            <span className="text-xs max-w-44 truncate">{j.symbol}</span>
                            <span>%{toFixed(j.ownershipPercentage)}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      {/* End Three CR Cards */}
    </div>
  );
}
