/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import React from "react";
import { useGetFocusRiskAssetIndustriesCRChart } from "@/queries/FundChartsAPI";
import { IDetailChartProps } from "@/app/(client)/details/[id]/components/charts/type";
import CRChart from "../CRChart";

function IndustriesCRChart({ id }: IDetailChartProps) {
  const params = {
    fundId: id
  };
  const { data: raw, isLoading } = useGetFocusRiskAssetIndustriesCRChart(params);
  const { data = [] } = raw || {};

  return <CRChart isLoading={isLoading} data={data} />;
}

export default IndustriesCRChart;
