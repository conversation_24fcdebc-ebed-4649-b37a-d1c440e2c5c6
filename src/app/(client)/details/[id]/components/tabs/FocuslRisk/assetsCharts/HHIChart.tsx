/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import React, { useMemo } from "react";
import HighChart from "@/components/Molecules/highChart";
import Loading from "@/components/Atoms/loading";
import ChartWrapper from "@/app/(client)/details/[id]/components/charts/chartLayouts/ChartWrapper";
import ChartFilterBox from "./ChartFilterBox";
import { getDynamicSeriesOfDataFormat } from "./utils";
import { IDetailChartProps, TDynamicSerieChart } from "./types";

function HHIChart({ graphPoints, isLoading }: IDetailChartProps) {
  const color = "#43E5A0";

  const hhiData: TDynamicSerieChart = {
    name: "HHI",
    checkable: false,
    graphPoints
  };

  const options = useMemo(
    () =>
      getDynamicSeriesOfDataFormat({
        data: [hhiData],
        visibleIndexes: [0],
        colors: [color]
      }),
    [hhiData, graphPoints]
  );

  const checkboxList = [
    {
      id: 1,
      title: hhiData.name,
      color,
      checkable: false,
      checked: false
    }
  ];

  return (
    <ChartWrapper summary={<ChartFilterBox filtersList={checkboxList} />}>
      {graphPoints?.length > 0 && <HighChart options={options} showTunedPeriod />}
      {!graphPoints?.length && !isLoading && (
        <div className="w-full h-full flex justify-center items-center align-middle text-xs font-normal pb-8">
          هیچ داده‌ای در این بازه وجود ندارد
        </div>
      )}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default HHIChart;
