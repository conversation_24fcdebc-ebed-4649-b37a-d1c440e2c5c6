/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-array-index-key */
import { useMemo } from "react";
import {
  useGetFocusRiskAssetBondCR,
  useGetFocusRiskAssetBondCRChart2,
  useGetFocusRiskAssetBondHHIChart2
} from "@/queries/FundChartsAPI";
import dayjs from "dayjs";
import { FUND_FOCUS } from "@/queries/fundsAPI/types";
import { getSeriesOfFocusCard } from "@/app/(client)/details/[id]/components/tabs/FocuslRisk/util";
import HoldersCards from "@/app/(client)/details/[id]/components/tabs/FocuslRisk/assetsCharts/HoldersCards";

export default function BondsHoldersCards(props: {
  fundId: string;
  activeCard: string;
  onCardClick: (id: string) => void;
}) {
  const { fundId, activeCard, onCardClick } = props;

  const filterYearly = dayjs().subtract(1, "year").format("YYYY-MM-DD");

  const params = {
    fundId,
    FromDate: filterYearly
  };

  const { data: rawHHI, isLoading: hhiChartLoading } = useGetFocusRiskAssetBondHHIChart2(params);

  const { data: bondCRChartData, isLoading: crChartLoading } = useGetFocusRiskAssetBondCRChart2(params);

  const { data: shareCR } = useGetFocusRiskAssetBondCR(fundId);

  const hhiPoints = rawHHI?.data?.fundsBondsHHIDiagramDetails?.map(hPoint => ({
    value: hPoint.value,
    checkpointDate: hPoint.checkPoint
  }));
  const cr1Points = bondCRChartData?.data?.map(c => ({ value: c.cr1, checkpointDate: c.checkpointDate }));
  const cr2Points = bondCRChartData?.data?.map(c => ({ value: c.cr2, checkpointDate: c.checkpointDate }));
  const cr3Points = bondCRChartData?.data?.map(c => ({ value: c.cr3, checkpointDate: c.checkpointDate }));

  const getDataSeries = (graphPoints: any[] | undefined, color: string) =>
    getSeriesOfFocusCard({
      graphPoints: graphPoints || [],
      color,
      field1: {
        id: "value"
      }
    });

  const hhiOptions = useMemo(() => getDataSeries(hhiPoints, "#43E5A0"), [rawHHI]);
  const cr1Options = useMemo(() => getDataSeries(cr1Points, "#2A9AB0"), [cr1Points]);
  const cr2Options = useMemo(() => getDataSeries(cr2Points, "#9B5E0D"), [cr2Points]);
  const cr3Options = useMemo(() => getDataSeries(cr3Points, "#8871BA"), [cr3Points]);

  const hhiData = [
    {
      id: FUND_FOCUS.HHI,
      title: "HHI",
      value: rawHHI?.data.lastValue,
      chartOption: hhiOptions
    }
  ];

  const crData = [
    {
      id: "c3",
      title: "CR1",
      value: shareCR?.data?.cr1.groupOwnerShipPercentage,
      chartOption: cr1Options,
      info: shareCR?.data?.cr1?.instruments.map(i => ({
        symbol: i.symbol,
        ownershipPercentage: i.ownershipPercentage
      }))
    },
    {
      id: "c4",
      title: "CR2",
      value: shareCR?.data?.cr2?.groupOwnerShipPercentage,
      chartOption: cr2Options,
      info: shareCR?.data?.cr2?.instruments.map(i => ({
        symbol: i.symbol,
        ownershipPercentage: i.ownershipPercentage
      }))
    },
    {
      id: "c5",
      title: "CR3",
      value: shareCR?.data?.cr3?.groupOwnerShipPercentage,
      chartOption: cr3Options,
      info: shareCR?.data?.cr3?.instruments.map(i => ({
        symbol: i.symbol,
        ownershipPercentage: i.ownershipPercentage
      }))
    }
  ];

  return (
    <HoldersCards
      hhiData={hhiData}
      crData={crData}
      activeCard={activeCard}
      onCardClick={onCardClick}
      crChartLoading={crChartLoading}
      hhiChartLoading={hhiChartLoading}
    />
  );
}
