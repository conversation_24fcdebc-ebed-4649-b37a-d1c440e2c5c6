"use client";

import React, { useMemo } from "react";
import Highcharts from "highcharts-v11";
import HighchartsTreeMap from "highcharts-v11/modules/treemap";
import HighchartsReact from "highcharts-react-official";
import { useGetConcentrationRiskRanges } from "@/queries/FundChartsAPI";
import { TTreeMapRange } from "@/queries/liquidityRiskApi/types";
import { DefaultColorScheme } from "@/components/Atoms/miniGauge/constants";
import { getMarketMapOptions } from "./util";
import { IMarketMapProps } from "./types";

if (typeof Highcharts === "object") {
  HighchartsTreeMap(Highcharts);
}

function MarketMapChart(props: IMarketMapProps) {
  const { rows = [], isError } = props;

  const { data: raw } = useGetConcentrationRiskRanges();

  const getColor = (n: number, ranges: TTreeMapRange[]) => {
    const f = ranges.find(i => n > i.min && n <= i.max);
    const level = f?.level || 0;
    return { color: DefaultColorScheme[level], colorIndex: level };
  };

  const options = useMemo(() => {
    const ranges = raw?.data?.sort((a, b) => a.max - b.max) || [];

    const series = rows.map(i => {
      const { color, colorIndex } = getColor(i.percent || 0, ranges);

      return {
        name: i.name,
        value: i.percent,
        percentage: i.percent,
        colorIndex,
        color
      };
    });

    const option = getMarketMapOptions(series);

    return option;
  }, [rows, raw]);

  return (
    <div className="flex w-full h-full grow relative">
      <div className="absolute w-full h-full left-0 top-0">
        {rows?.length > 0 && !isError ? (
          <HighchartsReact highcharts={Highcharts} options={options} containerProps={{ className: "h-full" }} />
        ) : (
          <div className="p-2 text-xs text-center">رکوردی برای نمایش وجود ندارد</div>
        )}
      </div>
    </div>
  );
}

export default MarketMapChart;
