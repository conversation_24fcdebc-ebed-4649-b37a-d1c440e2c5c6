/* eslint-disable object-shorthand */
/* eslint-disable func-names */

import { DefaultTextColorScheme } from "@/components/Atoms/miniGauge/constants";

const getPercent = (v1: number, v2: number) => {
  const sub = v2 - v1 > 0 ? v2 - v1 : 1;
  return 100 - Math.abs((sub / v1) * 100);
};

export const getMarketMapOptions = (data: any[]) => {
  const options = {
    chart: {
      margin: 0,
      backgroundColor: "transparent"
    },
    credits: {
      enabled: false
    },
    series: [
      {
        type: "treemap",
        layoutAlgorithm: "squarified",
        animation: false,
        clip: false,
        borderWidth: 2,
        borderColor: "#1F1F22",
        states: {
          hover: {
            brightness: 0,
            borderColor: "#1F1F22"
          }
        },
        data
      }
    ],
    title: {
      text: ""
    },
    tooltip: {
      useHTML: true,
      shadow: false,
      shared: true,
      outside: true,
      backgroundColor: "transparent",
      borderRadius: 0,
      borderWidth: 0,
      padding: 0,
      hideDelay: 0,
      shape: "rect",
      /* @ts-ignore */
      formatter: function () {
        /* @ts-ignore */
        // eslint-disable-next-line
        const { key, percentage, y } = this;

        return `<div class="market-tooltip font-yekan px-3 pt-3 pb-2 bg-bodyBackground border border-dark_black13 text-gray1 rounded-lg">
            <div class="text-xs leading-4 text-right">${key}</div>
            <div class="mt-2 text-sm leading-6 font-bold text-left">${percentage}%</div>
        </div>`;
      }
    },
    plotOptions: {
      treemap: {
        dataLabels: {
          useHTML: true,
          enabled: true,
          color: "#252529",
          align: "center",
          style: {
            textShadow: false,
            textOutline: false,
            fontSize: "13px",
            fontWeight: "700",
            fontFamily: "var(--font-yekan)"
          },
          /* @ts-ignore */
          formatter: function () {
            /* @ts-ignore */
            // eslint-disable-next-line
            const { key, percentage, colorIndex } = this;
            /* @ts-ignore */
            const w = this?.point?.shapeArgs?.width && this.point.shapeArgs.width;
            /* @ts-ignore */
            const h = this?.point?.shapeArgs?.height && this.point.shapeArgs.height;

            const boxSize = w < h ? w : h;

            const p = getPercent(boxSize || 0, 58);
            let zoom = 1;

            if (Number.isNaN(p)) {
              zoom = 0;
            } else if (p < 100) {
              zoom = boxSize / 100;
              zoom = zoom < 1 ? zoom : 1;
              zoom = zoom < 0.3 ? 0.3 : zoom;
            }

            if (boxSize / 100 < 0.2) {
              return "";
            }

            return `
              <div style='zoom:${zoom}; color:${DefaultTextColorScheme[colorIndex]}' class='scale-[${zoom}] flex text-2xl flex-col text-center mx-auto px-1 whitespace-nowrap rtl overflow-hidden'>
                <span class='w-[58px] truncate'>${key}</span>
                <span>${percentage}%</span>
              </div>
            `;
          }
        }
      }
    }
  };

  return options;
};

export const FilterColorsOwnerChart = [
  "#0C82F9",
  "filledYellow",
  "filledLightBlue",
  "filledBrown",
  "filledLightPurple",
  "filledGreen"
];

export const ChartColorsOwnerChart = ["#0C82F9", "#C7DA41", "#2A9AB0", "#9B5E0D", "#8871BA", "#43E5A0"];

export const FilterColorsDynamicGroupsChart = [
  "#0C82F9",
  "filledYellow",
  "filledLightBlue",
  "filledBrown",
  "filledLightPurple",
  "filledGreen"
];

export const ChartColorsDynamicGroupsChart = ["#0C82F9", "#C7DA41", "#2A9AB0", "#9B5E0D", "#8871BA", "#43E5A0"];
