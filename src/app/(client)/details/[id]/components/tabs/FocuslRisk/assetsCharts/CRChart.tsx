/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import React, { RefObject, useMemo, useRef, useState } from "react";
import HighChart from "@/components/Molecules/highChart";
import Loading from "@/components/Atoms/loading";
import ChartWrapper from "@/app/(client)/details/[id]/components/charts/chartLayouts/ChartWrapper";
import { TDynamicSerieChart } from "@/queries/FundChartsAPI/types";
import ChartFilterBox from "./ChartFilterBox";
import { ICRChartProps, TChartFilterItem } from "./types";
import { getDynamicSeriesOfDataFormat } from "../util";

function CRChart({ data, isLoading }: ICRChartProps) {
  const filterColors = ["filledLightBlue", "filledBrown", "filledLightPurple"];
  const chartColors = ["#2A9AB0", "#9B5E0D", "#8871BA"];

  const chartRef = useRef<{ chart: Highcharts.Chart; container: RefObject<HTMLDivElement> }>(null);
  const [checkboxList, setCheckboxList] = useState<TChartFilterItem[]>([
    {
      id: 0,
      title: "CR1",
      color: filterColors[0],
      checkable: true,
      checked: true
    },
    {
      id: 1,
      title: "CR2",
      color: filterColors[1],
      checkable: true,
      checked: true
    },
    {
      id: 2,
      title: "CR3",
      color: filterColors[2],
      checkable: true,
      checked: true
    }
  ]);

  const options = useMemo(() => {
    const cr1 = data?.map(i => ({ checkpointDate: i.checkpointDate, value: i.cr1 }));
    const cr2 = data?.map(i => ({ checkpointDate: i.checkpointDate, value: i.cr2 }));
    const cr3 = data?.map(i => ({ checkpointDate: i.checkpointDate, value: i.cr3 }));

    const crData: TDynamicSerieChart[] = [
      {
        name: "CR1",
        checkable: true,
        graphPoints: cr1 || []
      },
      {
        name: "CR2",
        checkable: true,
        graphPoints: cr2 || []
      },
      {
        name: "CR3",
        checkable: true,
        graphPoints: cr3 || []
      }
    ];

    return getDynamicSeriesOfDataFormat({
      data: crData,
      visibleIndexes: [0, 1, 2],
      colors: chartColors,
      hasPercentSign: true
    });
  }, [data]);

  const onFilterChange = (key: number) => {
    const updated = [...checkboxList];

    const f = updated.find(i => i.id === key);
    if (f) {
      f.checked = !f.checked;
      setCheckboxList(updated);

      const serie = chartRef?.current?.chart?.series?.find(i => i?.index === key);

      if (serie?.visible) {
        serie?.hide();
      } else {
        serie?.show();
      }
    }
  };

  return (
    <ChartWrapper summary={<ChartFilterBox filtersList={checkboxList} onFilterChange={onFilterChange} />}>
      {data && <HighChart options={options} refChart={chartRef} showTunedPeriod />}

      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default CRChart;
