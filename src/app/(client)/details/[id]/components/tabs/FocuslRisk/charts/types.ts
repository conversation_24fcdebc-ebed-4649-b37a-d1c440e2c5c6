export type TChartFilterItem = {
  id: number;
  title: string;
  color: string;
  checkable: boolean;
  checked: boolean;
};

export interface IOwnerChartProps {
  hasPercent?: boolean;
  filtersList?: TChartFilterItem[];
  onFilterChange?: (key: number) => void;
}

export type TMarketMapRows = {
  name: string;
  percent: number | null;
  count: number | null;
};
export interface IMarketMapProps {
  rows: TMarketMapRows[];
  isError?: boolean;
}
