/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import React from "react";
import { useGetFocusRiskAssetBondHHIChart } from "@/queries/FundChartsAPI";
import { IDetailChartProps } from "@/app/(client)/details/[id]/components/charts/type";
import HHIChart from "../HHIChart";

function BondsHHIChart({ id }: IDetailChartProps) {
  const params = {
    fundId: id
  };
  const { data: rawHHI, isLoading } = useGetFocusRiskAssetBondHHIChart(params);

  return <HHIChart graphPoints={rawHHI?.data?.fundsBondsHHIDiagramDetails || []} isLoading={isLoading} />;
}

export default BondsHHIChart;
