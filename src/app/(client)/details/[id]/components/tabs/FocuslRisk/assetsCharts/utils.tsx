import getLineChartOptions, { defaultColors } from "@/app/(client)/details/[id]/components/charts/util";
import { renderRecord } from "../util";
import { TDynamicSerieChart } from "./types";

export const getDynamicSeriesOfDataFormat = (props: {
  data: TDynamicSerieChart[];
  visibleIndexes: number[];
  hasPercent?: boolean;
  colors: string[];
}) => {
  const { data = [], visibleIndexes = [], hasPercent, colors } = props;

  const chartColors = colors || defaultColors;

  const series = data?.map((row, key) => {
    const count = row.graphPoints?.length || 0;

    const serie = row.graphPoints
      // ?.filter(item => item.value)

      ?.map((i, index) =>
        renderRecord({
          dateTime: i.checkPoint,
          value: i.value,
          index,
          count,
          color: chartColors?.[key] || null
        })
      );

    return {
      name: row.name,
      data: serie || [],
      type: "area",
      visible: visibleIndexes.includes(key),
      fillColor: "transparent"
    };
  });

  return getLineChartOptions({ data: series, hasPercent, chartColors });
};
