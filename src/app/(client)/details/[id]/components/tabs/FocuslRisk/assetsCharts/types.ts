import {
  IFundsBondsHHIDiagramDetailsItem,
  IFundsSharesHHIDiagramDetailsItem,
  IGetFocusRiskAssetBondCRChartData
} from "@/queries/FundChartsAPI/types";

export type TChartFilterItem = {
  id: number;
  title: string;
  color: string;
  checkable: boolean;
  checked: boolean;
};

export interface IOwnerChartProps {
  hasPercent?: boolean;
  filtersList?: TChartFilterItem[];
  onFilterChange?: (key: number) => void;
}

export interface IHoldersCardsProps {
  crData: ICrData[];
  hhiData?: IhhiData[];
  activeCard: string;
  onCardClick: (i: any) => void;
  crChartLoading?: boolean;
  hhiChartLoading?: boolean;
}

export interface ICrData {
  id: string;
  title: string;
  value?: number;
  chartOption: any;
  info?: {
    symbol: string;
    ownershipPercentage: number;
  }[];
}

export interface IhhiData {
  id: string;
  title: string;
  value?: number;
  chartOption: any;
}

export interface ICRChartProps {
  data: IGetFocusRiskAssetBondCRChartData[];
  isLoading: boolean;
}

export interface IDetailChartProps {
  isLoading: boolean;
  graphPoints: IFundsBondsHHIDiagramDetailsItem[];
}

export type TDynamicSerieChart = {
  name: string;
  checkable: boolean;
  graphPoints: IFundsSharesHHIDiagramDetailsItem[];
};
