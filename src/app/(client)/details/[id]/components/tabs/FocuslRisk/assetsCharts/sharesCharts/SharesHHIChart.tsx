/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import React from "react";

import { useGetFocusRiskAssetSharesHHIChart } from "@/queries/FundChartsAPI";
import { IDetailChartProps } from "@/app/(client)/details/[id]/components/charts/type";
import HHIChart from "../HHIChart";

function SharesHHIChart({ id }: IDetailChartProps) {
  const params = {
    fundId: id
  };
  const { data: rawHHI, isLoading } = useGetFocusRiskAssetSharesHHIChart(params);

  return <HHIChart graphPoints={rawHHI?.data?.fundsSharesHHIDiagramDetails || []} isLoading={isLoading} />;
}

export default SharesHHIChart;
