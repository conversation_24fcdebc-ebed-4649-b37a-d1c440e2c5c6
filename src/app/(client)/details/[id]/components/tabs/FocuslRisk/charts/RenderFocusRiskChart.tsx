import { FUND_FOCUS } from "@/queries/fundsAPI/types";
import OwnerChart from "./OwnerChart";
import HHIChart from "./HHIChart";
import CRChart from "./CRChart";
import AssociatedOwnersChart from "./AssociatedOwnersChart";
import DynamicGroupsChart from "./DynamicGroupsChart";
import { CHIPS_FOCUS } from "../util";

const RenderFocusRiskChart = ({ type, fundId, groupName }: { type: string; fundId: string; groupName?: string }) => {
  switch (type) {
    case FUND_FOCUS.OWNERS:
      return <OwnerChart id={fundId} />;
    case FUND_FOCUS.HHI:
      return <HHIChart id={fundId} />;
    case FUND_FOCUS.CR:
      return <CRChart id={fundId} />;
    case CHIPS_FOCUS.ASSOCIATE:
      return <AssociatedOwnersChart id={fundId} />;
    default:
      return <DynamicGroupsChart id={fundId} groupName={groupName} />;
  }
};

export default RenderFocusRiskChart;
