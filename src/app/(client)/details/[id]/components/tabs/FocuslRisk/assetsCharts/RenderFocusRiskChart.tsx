import { FUND_FOCUS_Assets } from "@/queries/fundsAPI/types";
import IndustriesCRChart from "@/app/(client)/details/[id]/components/tabs/FocuslRisk/assetsCharts/industriesCharts/IndustriesCRChart";
import IndustriesHHIChart from "@/app/(client)/details/[id]/components/tabs/FocuslRisk/assetsCharts/industriesCharts/IndustriesHHIChart";
import BondsHHIChart from "./bondsCharts/BondsHHIChart";
import SharesHHIChart from "./sharesCharts/SharesHHIChart";
import BondsCRChart from "./bondsCharts/BondsCRChart";
import SharesCRChart from "./sharesCharts/SharesCRChart";

const RenderFocusRiskChart = ({ type, fundId, activeChips }: { type: string; fundId: string; activeChips: string }) => {
  switch (type) {
    case FUND_FOCUS_Assets.HHI:
      if (activeChips === "1") return <SharesHHIChart id={fundId} />;
      if (activeChips === "2") return <BondsHHIChart id={fundId} />;
      if (activeChips === "3") return <IndustriesHHIChart id={fundId} />;
    // eslint-disable-next-line no-fallthrough
    case FUND_FOCUS_Assets.CR:
      if (activeChips === "1") return <SharesCRChart id={fundId} />;
      if (activeChips === "2") return <BondsCRChart id={fundId} />;
      if (activeChips === "3") return <IndustriesCRChart id={fundId} />;
    // eslint-disable-next-line no-fallthrough
    default:
      return <SharesHHIChart id={fundId} />;
  }
};

export default RenderFocusRiskChart;
