/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import React from "react";
import { useGetFocusRiskAssetIndustriesHHIChart } from "@/queries/FundChartsAPI";
import { IDetailChartProps } from "@/app/(client)/details/[id]/components/charts/type";
import HHIChart from "../HHIChart";

function IndustriesHHIChart({ id }: IDetailChartProps) {
  const params = {
    fundId: id
  };
  const { data: rawHHI, isLoading } = useGetFocusRiskAssetIndustriesHHIChart(params);

  return <HHIChart graphPoints={rawHHI?.data?.fundsIndustriesHHIDiagramDetails || []} isLoading={isLoading} />;
}

export default IndustriesHHIChart;
