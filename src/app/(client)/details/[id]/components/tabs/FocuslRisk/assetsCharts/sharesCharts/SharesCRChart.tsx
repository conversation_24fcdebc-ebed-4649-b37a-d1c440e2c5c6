/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import React from "react";
import { useGetFocusRiskAssetSharesCRChart } from "@/queries/FundChartsAPI";
import { IDetailChartProps } from "@/app/(client)/details/[id]/components/charts/type";
import CRChart from "../CRChart";

function SharesCRChart({ id }: IDetailChartProps) {
  const params = {
    fundId: id
  };
  const { data: raw, isLoading } = useGetFocusRiskAssetSharesCRChart(params);

  const { data = [] } = raw || {};

  return <CRChart isLoading={isLoading} data={data} />;
}

export default SharesCRChart;
