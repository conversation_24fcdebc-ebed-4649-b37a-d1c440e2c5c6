/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import React, { RefObject, useEffect, useMemo, useRef, useState } from "react";
import { useGetFocusRiskOwnershipPercentageChart } from "@/queries/FundChartsAPI";
import HighChart from "@/components/Molecules/highChart";
import Loading from "@/components/Atoms/loading";
import ChartWrapper from "@/app/(client)/details/[id]/components/charts/chartLayouts/ChartWrapper";
import { IDetailChartProps } from "@/app/(client)/details/[id]/components/charts/type";
import { getDynamicSeriesOfDataFormat } from "../util";
import ChartFilterBox from "./ChartFilterBox";
import { TChartFilterItem } from "./types";
import { ChartColorsOwnerChart as chartColors, FilterColorsOwnerChart as filterColors } from "./util";

function OwnerChart({ id }: IDetailChartProps) {
  const chartRef = useRef<{ chart: Highcharts.Chart; container: RefObject<HTMLDivElement> }>(null);
  const [checkboxList, setCheckboxList] = useState<TChartFilterItem[]>([]);

  const { data: ownerRaw, isLoading } = useGetFocusRiskOwnershipPercentageChart({ FundId: id });
  const { data: ownerData = [] } = ownerRaw || {};

  const visibleIndexes = [0];

  const options = useMemo(() => {
    const formated = ownerData.map(i => ({
      ...i,
      graphPoints: i.graphpoints.map(j => ({ checkpointDate: j.checkpointDate, value: j.value }))
    }));

    return getDynamicSeriesOfDataFormat({
      data: formated,
      visibleIndexes,
      colors: chartColors,
      hasPercentSign: true
    });
  }, [ownerData]);

  useEffect(() => {
    if (ownerData) {
      const list = ownerData.map((i, k) => ({
        id: k,
        title: i.name,
        color: filterColors?.[k],
        checkable: i.checkable,
        checked: visibleIndexes.includes(k)
      }));

      setCheckboxList(list);
    }
  }, [ownerRaw]);

  const onFilterChange = (key: number) => {
    const updated = [...checkboxList];

    const f = updated.find(i => i.id === key);
    if (f) {
      f.checked = !f.checked;
      setCheckboxList(updated);

      const serie = chartRef?.current?.chart?.series?.find(i => i?.index === key);

      if (serie?.visible) {
        serie?.hide();
      } else {
        serie?.show();
      }
    }
  };

  return (
    <ChartWrapper summary={<ChartFilterBox filtersList={checkboxList} onFilterChange={onFilterChange} />}>
      {ownerRaw?.data && ownerRaw?.data?.length > 0 && (
        <HighChart options={options} refChart={chartRef} showTunedPeriod />
      )}
      {!ownerRaw?.data?.length && !isLoading && (
        <div className="w-full h-full flex justify-center items-center align-middle text-xs font-normal pb-8">
          هیچ داده‌ای در این بازه وجود ندارد
        </div>
      )}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex">
          <Loading size="lg" />
        </div>
      )}
    </ChartWrapper>
  );
}

export default OwnerChart;
