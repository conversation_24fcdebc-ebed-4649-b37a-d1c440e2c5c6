import DatePickerFill from "@/assets/icons/calender-fill.svg";
import DatePicker from "@/assets/icons/datePicker.svg";
import ArrowDown from "@/assets/icons/down-arrow.svg";
import FilterDropDown from "@/components/Molecules/filterDropDown/FilterDropDown";
import { DatePickerWrapper } from "@/components/Organism/clientDatePicker";
import { IDatePickerWrapperRefProps } from "@/components/Organism/clientDatePicker/types";
import { dateConverter } from "@/utils/DateHelper";
import Tippy from "@tippyjs/react";
import { useRef } from "react";
import { twMerge } from "tailwind-merge";
import styles from "./booletLine.module.scss";
import { IEvaluationHistoryModalDetails } from "./types";
import { EvaluationItems, EvaluationItemsWithColor } from "./utils";

function EvaluationInstructionHistoryModalDetail({
  title,
  description,
  totalCount,
  indexDate,
  EvaluationHistoryList,
  setEvaluationHistoryFilter,
  evaluationHistoryFilter,
  setSelectedRange,
  selectedRange
}: IEvaluationHistoryModalDetails) {
  const datePickerRef = useRef<IDatePickerWrapperRefProps>(null);

  const convertDate = (i: Date) => {
    const newDate = new Date(i);
    return newDate?.toISOString();
  };

  return (
    <div className=" bg-backgroundCardBackground min-h-[337px]">
      <div className="  pt-4 border-t border-t-borderBorderAndDivider -mt-1">
        <div className="flex justify-between text-xs text-secondaryText">
          <div>{title}</div>
          <div>{dateConverter(indexDate).format("HH:mm - YYYY/MM/DD")}</div>
        </div>
        <div className="text-sm leading-6 text-borderLightGray pt-2 font-normal">{description}</div>
        <div className="flex justify-between pt-6">
          <div className="flex items-center gap-[2px]">
            <div className="text-sm leading-6 font-bold text-borderLightGray">تاریخچه ارزیابی ها</div>
            <div className="text-xs leading-4 text-borderLightGray">{`(${totalCount} مورد)`}</div>
          </div>
          <div className="flex items-center gap-3">
            <FilterDropDown
              defaultTitle="همه"
              options={EvaluationItems}
              RenderArrow={<ArrowDown className="text-borderLightGray w-4 h-4" />}
              defaultTitleClassName="leading-4 text-[10px]"
              titleParentClassName="border border-1 border-borderBorderAndDivider rounded py-[3px] pr-3 pl-2 w-[104px]"
              listClassName="w-[106px] [&_.item-text]:!text-[10px]"
              onSelect={(v: any) => {
                setEvaluationHistoryFilter(v);
              }}
              value={evaluationHistoryFilter}
              showValueOnTitle
            />

            <DatePickerWrapper
              isRange
              ref={datePickerRef}
              closeOnConfirm={false}
              hasFooter
              config={{
                locale: "fa-IR",
                weekends: ["friday"],
                maxDate: new Date()
              }}
              initialValue={selectedRange}
              className="-top-[6px] "
              onConfirm={(date?: Date[]) => {
                if (!date) return;

                setSelectedRange(date[1] ? [date[0], date[1]] : [date[0]]);

                if (date[0] && date[1]) {
                  datePickerRef.current?.close();
                }
              }}
              onCancel={() => {
                setSelectedRange([]);
              }}
            >
              {selectedRange?.[0] && selectedRange?.[1] ? (
                <Tippy
                  placement="top-start"
                  zIndex={9999}
                  arrow
                  className={twMerge("border border-[#545454]  !bg-[#28282C] rounded-sm")}
                  content={
                    <div className="text-borderLightGray text-[10px] font-bold p-2 flex gap-1.5">
                      <div>از</div>
                      <div>{dateConverter(convertDate(selectedRange?.[0])).format("YYYY/MM/DD")}</div>
                      <div>تا</div>
                      <div>{dateConverter(convertDate(selectedRange?.[1])).format("YYYY/MM/DD")}</div>
                    </div>
                  }
                >
                  <div className="border border-1 border-semanticPrimary2 rounded p-[3px] cursor-pointer">
                    <DatePickerFill className="text-semanticPrimary2 w-4 h-4" />
                  </div>
                </Tippy>
              ) : (
                <div className="border border-1 border-secondaryText rounded p-[3px] cursor-pointer">
                  <DatePicker className="text-secondaryText w-4 h-4" />
                </div>
              )}
            </DatePickerWrapper>
          </div>
        </div>
        <div className="relative bg-dark_black9  pb-0.5 pt-1 pr-[7px] rounded-lg mt-2 h-[216px]  overflow-y-hidden">
          <div className="overflow-auto ml-1  h-full  pl-1 pt-1">
            <div className={twMerge(styles.boolet)}>
              <ul>
                {EvaluationHistoryList &&
                  EvaluationHistoryList?.map((item, index) => {
                    const evaluationItemFounded = EvaluationItemsWithColor.find(i => i.id === item.status);

                    return (
                      // eslint-disable-next-line react/no-array-index-key
                      <li key={index}>
                        <div className="flex items-center justify-between gap-4 pr-1 pb-6">
                          <div className={twMerge("text-xs")} style={{ color: evaluationItemFounded?.color }}>
                            {evaluationItemFounded?.label}
                          </div>
                          <div className="text-xs text-secondaryText">
                            {dateConverter(item.date).format("HH:mm - YYYY/MM/DD")}
                          </div>
                        </div>
                      </li>
                    );
                  })}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EvaluationInstructionHistoryModalDetail;
