import Loading from "@/components/Atoms/loading";
import { DropdownItem } from "@/queries/adaptiveRiskAPI/types";
import { useGetEvaluationInstructionHistoryQuery } from "@/queries/operationRiskWithStrategyAPI/OperationRiskWithStrategyApi";
import { useUserTableStore } from "@/store/userTable";
import { dateConverter } from "@/utils/DateHelper";
import { useCallback, useState } from "react";
import EvaluationInstructionHistoryModalDetail from "./EvaluationInstructionHistoryModalDetail";

function EvaluationInstructionHistoryModal({ rankId }: { rankId: string }) {
  const { filters } = useUserTableStore();

  const [evaluationHistoryFilter, setEvaluationHistoryFilter] = useState<DropdownItem>();

  const [selectedRange, setSelectedRange] = useState<Date[] | undefined>();

  const { data, isLoading, isFetching } = useGetEvaluationInstructionHistoryQuery(
    {
      IndexId: rankId,
      Status: evaluationHistoryFilter?.id as number,
      EvaluationDateFrom:
        selectedRange?.[0] && selectedRange?.[1]
          ? dateConverter(selectedRange?.[0]).calendar("gregory").format("YYYY-MM-DD")
          : undefined,
      EvaluationDateTo: selectedRange?.[1]
        ? dateConverter(selectedRange?.[1]).calendar("gregory").format("YYYY-MM-DD")
        : undefined,
      ...filters
    },
    !(selectedRange?.[0] && !selectedRange?.[1])
  );

  const handleSetEvaluationHistoryFilter = useCallback(setEvaluationHistoryFilter, [setEvaluationHistoryFilter]);
  const handleSetSelectedRange = useCallback(setSelectedRange, [setSelectedRange]);

  return (
    <div>
      {isFetching || isLoading ? (
        <div className="flex items-center justify-center h-[348px] bg-backgroundCardBackground">
          <Loading />
        </div>
      ) : (
        data?.data && (
          <EvaluationInstructionHistoryModalDetail
            title={data?.data?.indexTitle}
            description={data?.data?.indexDescription}
            totalCount={data?.data?.indexesEvaluations?.length}
            indexDate={data?.data?.indexDate}
            EvaluationHistoryList={data?.data?.indexesEvaluations}
            evaluationHistoryFilter={evaluationHistoryFilter}
            setEvaluationHistoryFilter={handleSetEvaluationHistoryFilter}
            setSelectedRange={handleSetSelectedRange}
            selectedRange={selectedRange}
          />
        )
      )}
    </div>
  );
}

export default EvaluationInstructionHistoryModal;
