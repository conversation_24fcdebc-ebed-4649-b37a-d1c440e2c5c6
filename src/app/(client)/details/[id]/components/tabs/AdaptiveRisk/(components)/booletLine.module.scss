.boolet {
  :global {
    ul {
      position: relative;
      padding-right: 18px;
      list-style: none !important;

      & > li {
        cursor: pointer;
        position: relative;
        counter-increment: list;

        // &:not(:last-child) {
        //   padding-bottom: 2px;
        // }

        &::after {
          content: "";
          display: inline-block;
          position: absolute;
          top: 2px;
          right: -17px;
          width: 12px;
          height: 12px;
          border: 1px solid #bdbdbd;
          border-radius: 50%;
          background-color: #bdbdbd;
        }

        &::before {
          content: "";
          display: inline-block;
          position: absolute;
          top: 2px;
          right: -11.5px;
          width: 10px;
          height: 100%;
        }

        &:first-child {
          &::before {
            border-right: 1px solid #bdbdbd;
          }
        }

        &:not(:first-child) {
          &::before {
            border-right: 1px dashed #bdbdbd;
          }
        }

        &:last-child {
          &::before {
            border-right: none;
          }
        }
      }
    }
  }
}
