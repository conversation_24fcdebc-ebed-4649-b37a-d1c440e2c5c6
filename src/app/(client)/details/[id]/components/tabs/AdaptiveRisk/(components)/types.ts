import { IGetEvaluationInstructionHistoryItems } from "@/queries/operationRiskWithStrategyAPI/types";

export interface DropdownItem {
  id: string | number;
  label: string;
}

export interface IEvaluationHistoryModalDetails {
  title: string;
  description: string;
  totalCount: number;
  indexDate: string;
  EvaluationHistoryList: IGetEvaluationInstructionHistoryItems[];
  setEvaluationHistoryFilter: (i: DropdownItem) => void;
  evaluationHistoryFilter?: DropdownItem;
  setSelectedRange: (i: Date[]) => void;
  selectedRange: Date[] | undefined;
}
