import AdjustmentHeatLineCard from "@/components/Molecules/AdjustmentHeatLineCard";
import { useGetFundAdaptiveRisksListQuery } from "@/queries/fundsAPI";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { useState } from "react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import AdaptiveInstructions from "../../cards/AdaptiveInstructions";
import { IAdaptiveRisksProps } from "../types";
import ChartAndTable from "./(components)/ChartAndTable";

function AdaptiveRisk(props: IAdaptiveRisksProps) {
  const { fundId } = props;
  const { data: fundQuery } = useGetFundAdaptiveRisksListQuery(fundId);

  const [totalCount, setTotalCount] = useState<undefined | number>(undefined);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { enable_CamplianceRisk_Tab_QuorumOfFundsAssets } = useModulesStore();

  return (
    <div className="flex flex-col border-t border-dark_black8 grow h-full">
      {!!enable_CamplianceRisk_Tab_QuorumOfFundsAssets && !!fundQuery?.data?.quorums?.length && (
        <div className="mt-3" aria-roledescription="status">
          {fundQuery?.data?.quorumOfFundType === 100 ? (
            <h5 className="leading-[24px]">وضعیت حدنصاب دارایی صندوق</h5>
          ) : (
            <h5 className="leading-[24px]">وضعیت حدنصاب صنایع صندوق</h5>
          )}

          <div className="mt-2">
            <Swiper
              dir="rtl"
              slidesPerView="auto"
              keyboard
              grabCursor
              spaceBetween={8}
              modules={[Navigation, Keyboard, Mousewheel]}
            >
              {fundQuery?.data?.quorums?.map((i, index) => (
                <SwiperSlide key={i?.title} className="min-w-[275.2px] max-w-fit min-h-[83px]">
                  <AdjustmentHeatLineCard
                    value={i.value}
                    lowValue={i.lowValue}
                    highValue={i.highValue}
                    title={i.title}
                    type={(index + 1) * 100}
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>

          {/* <div className="grid grid-flow-col auto-cols-fr gap-2 mt-2 min-h-[83px]">
            {fundQuery?.data?.quorums?.map((i, index) => (
              <AdjustmentHeatLineCard
                value={i.value}
                lowValue={i.lowValue}
                highValue={i.highValue}
                title={i.title}
                type={(index + 1) * 100}
              />
            ))}
          </div> */}
        </div>
      )}

      <div className="flex items-center justify-between mt-4 pb-2 ">
        <h3 className="text-base ">دستورالعمل ها</h3>
        <span className="text-[#F4F4F4] text-sm">{totalCount} مورد</span>
      </div>
      <div className="flex gap-2 bg-transparent grow ">
        <AdaptiveInstructions fundId={fundId} />
        <ChartAndTable fundId={fundId} setTotalCount={setTotalCount} />
      </div>
    </div>
  );
}

export default AdaptiveRisk;
