import AdjustmentHeatLineCard from "@/components/Molecules/AdjustmentHeatLineCard";
import { useGetFundAdaptiveRisksListQuery } from "@/queries/fundsAPI";
import useModulesStore from "@/store/modulesStore/ModulesStore";
import { useState, useRef } from "react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { Swiper as SwiperClass } from "swiper/types";
import AdaptiveInstructions from "../../cards/AdaptiveInstructions";
import { IAdaptiveRisksProps } from "../types";
import ChartAndTable from "./(components)/ChartAndTable";

// Arrow Icon Component
const ArrowIcon = ({ direction, isActive }: { direction: "left" | "right"; isActive: boolean }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    className={`transform ${direction === "right" ? "rotate-180" : ""}`}
  >
    <path
      d="M9 18L15 12L9 6"
      stroke={isActive ? "#10B981" : "#6B7280"}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

function AdaptiveRisk(props: IAdaptiveRisksProps) {
  const { fundId } = props;
  const { data: fundQuery } = useGetFundAdaptiveRisksListQuery(fundId);

  const [totalCount, setTotalCount] = useState<undefined | number>(undefined);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const swiperRef = useRef<SwiperClass | null>(null);

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { enable_CamplianceRisk_Tab_QuorumOfFundsAssets } = useModulesStore();

  const handlePrevSlide = () => {
    swiperRef.current?.slidePrev();
  };

  const handleNextSlide = () => {
    swiperRef.current?.slideNext();
  };

  const handleSlideChange = (swiper: SwiperClass) => {
    // In RTL mode, we need to check the actual slide states
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  const needsNavigation = fundQuery?.data?.quorums && fundQuery.data.quorums.length > 4;

  return (
    <div className="flex flex-col border-t border-dark_black8 grow h-full">
      {!!enable_CamplianceRisk_Tab_QuorumOfFundsAssets && !!fundQuery?.data?.quorums?.length && (
        <div className="mt-3" aria-roledescription="status">
          {fundQuery?.data?.quorumOfFundType === 100 ? (
            <h5 className="leading-[24px]">وضعیت حدنصاب دارایی صندوق</h5>
          ) : (
            <h5 className="leading-[24px]">وضعیت حدنصاب صنایع صندوق</h5>
          )}

          <div className="mt-2 relative">
            {/* Navigation Arrows */}
            {needsNavigation && (
              <>
                <button
                  onClick={handleNextSlide}
                  disabled={isEnd}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                    isEnd ? "bg-gray-800 cursor-not-allowed" : "bg-gray-700 hover:bg-gray-600 cursor-pointer"
                  }`}
                  style={{ left: "-16px" }}
                >
                  <ArrowIcon direction="left" isActive={!isEnd} />
                </button>

                <button
                  onClick={handlePrevSlide}
                  disabled={isBeginning}
                  className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                    isBeginning ? "bg-gray-800 cursor-not-allowed" : "bg-gray-700 hover:bg-gray-600 cursor-pointer"
                  }`}
                  style={{ right: "-16px" }}
                >
                  <ArrowIcon direction="right" isActive={!isBeginning} />
                </button>
              </>
            )}

            <Swiper
              dir="rtl"
              slidesPerView="auto"
              keyboard
              grabCursor
              spaceBetween={8}
              modules={[Navigation, Keyboard, Mousewheel]}
              onSwiper={swiper => {
                swiperRef.current = swiper;
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              onSlideChange={handleSlideChange}
            >
              {fundQuery?.data?.quorums?.map((i, index) => (
                <SwiperSlide key={i?.title} className="min-w-[275.2px] max-w-fit min-h-[83px]">
                  <AdjustmentHeatLineCard
                    value={i.value}
                    lowValue={i.lowValue}
                    highValue={i.highValue}
                    title={i.title}
                    type={(index + 1) * 100}
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between mt-4 pb-2 ">
        <h3 className="text-base ">دستورالعمل ها</h3>
        <span className="text-[#F4F4F4] text-sm">{totalCount} مورد</span>
      </div>
      <div className="flex gap-2 bg-transparent grow ">
        <AdaptiveInstructions fundId={fundId} />
        <ChartAndTable fundId={fundId} setTotalCount={setTotalCount} />
      </div>
    </div>
  );
}

export default AdaptiveRisk;
