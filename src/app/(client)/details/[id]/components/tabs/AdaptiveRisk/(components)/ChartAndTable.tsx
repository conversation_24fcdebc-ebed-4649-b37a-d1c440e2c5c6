import { IFilterTimeTab } from "@/app/(client)/details/[id]/components/tabs/types";
import ChartIcon from "@/assets/icons/adaptive-chart.svg";
import TableIcon from "@/assets/icons/adaptive-table.svg";
import Calendar from "@/assets/icons/calendar-filter.svg";
import Close from "@/assets/icons/Cancel.svg";
import Search from "@/assets/icons/detail-search.svg";
import Input from "@/components/Atoms/input";
import ColorfulBar<PERSON>hart from "@/components/Molecules/ColorfulBarChart/ColorfulBarChart";
import Styles from "@/components/Molecules/ColorfulBarChart/ColorfulBarChart.module.scss";
import SwitchTab from "@/components/Molecules/switchTab/SwitchTab";
import { DatePickerWrapper } from "@/components/Organism/clientDatePicker";
import { IDatePickerWrapperRefProps } from "@/components/Organism/clientDatePicker/types";
import AdaptiveRisTable from "@/containers/adaptiveRiskTable/AdaptiveRiskTable";
import { FILTER_TIME, getPeriodDate, getPrevDate, VISUALIZER_TYPE } from "@/containers/adaptiveRiskTable/utils";
import { useGetFundInstructionsIndexesEvaluationsDateRangeQuery } from "@/queries/fundInstructionsApi";
import { useGetAllFundInstructionDiagramQuery } from "@/queries/fundsAPI";
import useUserModalStore from "@/store/userModalStore";
import { dateConverter, getPersianYear } from "@/utils/DateHelper";
import dayjs from "dayjs";
import { parseAsString, useQueryStates } from "nuqs";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";
import EvaluationInstructionHistoryModal from "./EvaluationInstructionHistoryModal";

const visualizerTypeItems = [
  { id: VISUALIZER_TYPE.Table, icon: <TableIcon /> },
  { id: VISUALIZER_TYPE.Chart, icon: <ChartIcon /> }
];
export const getNow = () => dayjs().format("YYYY-MM-DD");

interface IChartAndTableProps {
  fundId: string;
  isChartOpen?: boolean;
  setTotalCount: (value: number) => void;
  isModal?: boolean;
}

function ChartAndTable({ fundId, isChartOpen, setTotalCount, isModal }: IChartAndTableProps) {
  const { openUserModal, isOpen, closeUserModal } = useUserModalStore();
  const gridRef = useRef<any>(null);
  const initialStartDate = getPrevDate(FILTER_TIME.Month3);
  const [filterText, setFilterText] = useState("");

  const { data: firstEvaluationDateData } = useGetFundInstructionsIndexesEvaluationsDateRangeQuery(fundId);

  const [queryStates, setQueryStates] = useQueryStates({
    expanded: parseAsString.withDefault("false"),
    openHistoryModalId: parseAsString.withDefault(""),
    toDate: parseAsString.withDefault(""),
    fromDate: parseAsString.withDefault(initialStartDate),
    filterTimeTab: parseAsString.withDefault(FILTER_TIME.Month3),
    visualizerTypeTab: parseAsString.withDefault(isChartOpen ? visualizerTypeItems[1].id : visualizerTypeItems[0].id)
  });

  const datePickerRef = useRef<IDatePickerWrapperRefProps>(null);

  const [period, setPeriod] = useState<string | number>(FILTER_TIME.Month3);
  const { toDate, fromDate, filterTimeTab, visualizerTypeTab } = queryStates;
  const [initialDate, setInitialDate] = useState<Date[] | undefined>(
    fromDate && toDate ? [new Date(fromDate), new Date(toDate)] : undefined
  );
  // eslint-disable-next-line @typescript-eslint/no-unused-vars

  const { data: chartData } = useGetAllFundInstructionDiagramQuery({
    fundId,
    EvaluationDateFrom: fromDate,
    EvaluationDateTo: toDate
  });

  const onSwitch = useCallback(
    (v: IFilterTimeTab, firstRender?: boolean) => {
      // using if to stop repetitive user click

      if (v.id !== filterTimeTab || firstRender) {
        // convert enum to date
        const date = getPrevDate(v.id);
        const now = getNow();
        setQueryStates({ filterTimeTab: v.id });

        if (v.id !== FILTER_TIME.DateRange) {
          setPeriod(v.id);
          setQueryStates({ fromDate: date });
          setQueryStates({ toDate: now });
          setInitialDate(undefined);
        }
      }
    },
    [filterTimeTab]
  );

  const onSearch = (value: string) => {
    gridRef.current?.onSearch(value);
    setFilterText(value);
  };

  const onMaximize = () => {
    if (isOpen) {
      closeUserModal();
    } else {
      openUserModal(
        <ChartAndTable
          isChartOpen={visualizerTypeTab === VISUALIZER_TYPE.Chart}
          fundId={fundId}
          setTotalCount={setTotalCount}
          isModal
        />,
        {
          center: true,
          width: "100%",
          height: "100%",
          className: "p-3 bg-black",
          rootClassName: Styles.maximizeModal
        }
      );
    }
  };

  const filterTimeItems = useMemo(
    () => [
      {
        id: FILTER_TIME.DateRange,
        icon: (
          <DatePickerWrapper
            isRange
            ref={datePickerRef}
            closeOnConfirm={false}
            hasFooter
            initialValue={initialDate}
            config={{
              yearRangeFrom:
                firstEvaluationDateData &&
                Number(getPersianYear(new Date(firstEvaluationDateData?.data?.firstEvaluationDate))),
              locale: "fa-IR",
              weekends: ["friday"],
              maxDate: new Date(),
              minDate: firstEvaluationDateData && new Date(firstEvaluationDateData?.data?.firstEvaluationDate)
            }}
            className="-top-[6px]"
            onConfirm={(date?: Date[]) => {
              if (date && date[0] && date[1] && date !== initialDate) {
                const range = getPeriodDate(date);
                setPeriod(range);

                setInitialDate(date);
                setQueryStates({ fromDate: dateConverter(date[0]).calendar("gregory").format("YYYY-MM-DD") });
                setQueryStates({ toDate: dateConverter(date[1]).calendar("gregory").format("YYYY-MM-DD") });
                datePickerRef.current?.close();

                const f = filterTimeItems.find(i => i.id === FILTER_TIME.DateRange);

                if (f) {
                  onSwitch(f);
                }
              }
            }}
            onCancel={() => {
              setInitialDate(undefined);
              onSwitch(filterTimeItems[1]);
            }}
          >
            <Calendar />
          </DatePickerWrapper>
        )
      },
      { id: FILTER_TIME.Month3, title: "3 ماه" },
      { id: FILTER_TIME.Month6, title: "6 ماه" },
      { id: FILTER_TIME.Yearly, title: "1 سال" }
    ],
    [initialDate, onSwitch, firstEvaluationDateData]
  );

  const updateVisualizerTypeTab = (newTabId: string) => {
    setQueryStates(prevQueryStates => ({ ...prevQueryStates, visualizerTypeTab: newTabId }));
  };

  useEffect(() => {
    if (queryStates?.expanded === "true") {
      openUserModal(
        <ChartAndTable
          isChartOpen={visualizerTypeTab === VISUALIZER_TYPE.Chart}
          fundId={fundId}
          setTotalCount={setTotalCount}
          isModal
        />,
        {
          center: true,
          width: "100%",
          height: "100%",
          className: "p-3 bg-black",
          rootClassName: Styles.maximizeModal,
          afterClose: () => {
            closeUserModal();
            setQueryStates({ expanded: "false" });
          }
        }
      );
    }

    if (queryStates?.openHistoryModalId) {
      openUserModal(<EvaluationInstructionHistoryModal rankId={queryStates?.openHistoryModalId} />, {
        headerTitle: "ارزیابی های انجام شده",
        center: true,
        width: 512,
        afterClose: () => {
          closeUserModal();
          setQueryStates({ openHistoryModalId: "" });
        }
      });
    }
  }, [queryStates?.expanded, queryStates?.openHistoryModalId]);

  // percentTabItems.find(i => i.id === percentTab) || percentTabItems[0]

  // filterTimeTab = filterTimeItems.find((i) => i.id ===);

  return (
    <div className={twMerge("grow rounded-lg bg-backgroundCardBackground h-full", isModal && "p-0")}>
      <div className="flex flex-col h-full justify-start rounded-lg bg-[#343438]">
        <div className="flex items-center justify-between  pb-2 p-2">
          {visualizerTypeTab === VISUALIZER_TYPE.Table ? (
            <Input
              value={filterText}
              onChange={onSearch}
              preserveErrorMessage={false}
              inputSize="small"
              placeHolder="جستجو"
              startAdornment={<Search />}
              wrapperClassName="mb-0"
              className="w-[208px] mb-0"
              data-test="search-input-adaptive-risk"
            />
          ) : (
            <span className="text-sm text-white font-normal">نمودار دستور العمل</span>
          )}

          <div className="flex items-center gap-4">
            <div>
              <div className="relative p-0.5  flex items-center bg-[#28282C] rounded w-fit ">
                <SwitchTab
                  items={filterTimeItems}
                  activeTab={filterTimeTab}
                  onSwitch={v => {
                    onSwitch(v);
                    if (v.id === FILTER_TIME.DateRange) datePickerRef.current?.open();
                  }}
                  itemClassName="px-[14px] [&:nth-child(3)]:px-[16px]  first-of-type:px-[9px] last-of-type:px-3 "
                />
              </div>
            </div>

            <div>
              <div className="relative p-0.5  flex items-center bg-[#28282C] rounded w-fit ">
                <SwitchTab
                  items={visualizerTypeItems}
                  activeTab={visualizerTypeTab}
                  onSwitch={newTab => updateVisualizerTypeTab(newTab.id)}
                  itemClassName="px-[7px]"
                  variant="filled"
                />
              </div>
            </div>
            {isModal && (
              <div onClick={closeUserModal}>
                <Close className="w-6 h-6 cursor-pointer" />
              </div>
            )}
          </div>
        </div>

        {visualizerTypeTab === VISUALIZER_TYPE.Chart ? (
          <ColorfulBarChart
            onMaximize={() => {
              onMaximize();
              setQueryStates({ expanded: "true" });
            }}
            data={chartData?.data}
          />
        ) : (
          <AdaptiveRisTable
            onMaximize={() => {
              onMaximize();
              setQueryStates({ expanded: "true" });
            }}
            onOpenHistoryModal={id => setQueryStates({ openHistoryModalId: id })}
            setTotalCount={setTotalCount}
            fundId={fundId}
            evaluationDateFrom={fromDate}
            evaluationDateTo={toDate}
            textFilter={filterText}
            onCloseHistoryModal={() => {
              if (isModal) {
                onMaximize();
                setQueryStates({ expanded: "true" });
              }
            }}
          />
        )}
      </div>
    </div>
  );
}

export default ChartAndTable;
