"use client";

import FundCards from "@/containers/home/<USER>";
import { parseAsString, useQueryStates } from "nuqs";
import DropdownChips from "@/containers/home/<USER>";

function Home() {
  const [queryStates, setQueryStates] = useQueryStates(
    {
      fundsType: parseAsString
    },
    {
      history: "replace"
    }
  );

  return (
    <div className="px-6 pb-6 pt-4">
      <div className="flex">
        <div className="text-sm leading-6 font-bold pl-1">دسته بندی صندوق:</div>

        <DropdownChips setQueryStates={setQueryStates} queryStates={queryStates.fundsType} />
      </div>

      <FundCards filterByFundId={queryStates.fundsType} />
    </div>
  );
}

export default Home;
